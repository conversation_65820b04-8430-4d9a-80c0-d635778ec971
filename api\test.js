export default async function handler(req, res) {
  // Allow both GET and POST for testing
  try {
    const RESEND_API_KEY = process.env.RESEND_API_KEY;
    
    // Check if API key exists
    if (!RESEND_API_KEY) {
      return res.status(500).json({ 
        error: 'RESEND_API_KEY not found in environment variables',
        debug: 'Environment variable is missing',
        timestamp: new Date().toISOString()
      });
    }

    // Check if API key format looks correct
    if (!RESEND_API_KEY.startsWith('re_')) {
      return res.status(500).json({ 
        error: 'Invalid API key format',
        debug: 'API key should start with re_',
        timestamp: new Date().toISOString()
      });
    }

    return res.status(200).json({
      success: true,
      message: 'API is working!',
      apiKeyExists: true,
      apiKeyFormat: 'Valid (starts with re_)',
      timestamp: new Date().toISOString(),
      method: req.method
    });

  } catch (error) {
    return res.status(500).json({
      error: 'Test failed',
      details: error.message,
      timestamp: new Date().toISOString()
    });
  }
}
