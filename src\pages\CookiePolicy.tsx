import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart3, Shield } from 'lucide-react';

const CookiePolicy: React.FC = () => {
  const cookieTypes = [
    {
      title: "Essential Cookies",
      icon: Shield,
      description: "These cookies are necessary for the website to function properly and cannot be disabled.",
      examples: [
        "Session management cookies",
        "Security cookies",
        "Load balancing cookies",
        "Accessibility preference cookies"
      ],
      color: "emerald"
    },
    {
      title: "Analytics Cookies",
      icon: BarChart3,
      description: "These cookies help us understand how visitors interact with our website.",
      examples: [
        "Google Analytics cookies",
        "Page view tracking",
        "User behavior analysis",
        "Performance monitoring"
      ],
      color: "blue"
    },
    {
      title: "Functional Cookies",
      icon: Settings,
      description: "These cookies enable enhanced functionality and personalization.",
      examples: [
        "Language preferences",
        "Theme preferences",
        "Form data retention",
        "User interface customization"
      ],
      color: "purple"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      {/* Header */}
      <div className="bg-gradient-to-r from-emerald-600 to-emerald-500 text-white py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <Cookie className="w-16 h-16 mx-auto mb-4" />
            <h1 className="text-4xl font-bold mb-4">Cookie Policy</h1>
            <p className="text-xl text-emerald-100">
              Learn about how we use cookies to improve your browsing experience.
            </p>
            <p className="text-emerald-200 mt-2">Last updated: {new Date().toLocaleDateString()}</p>
          </motion.div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="prose prose-lg max-w-none"
        >
          <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">What Are Cookies?</h2>
            <p className="text-gray-600 leading-relaxed mb-4">
              Cookies are small text files that are stored on your device when you visit our website. 
              They help us provide you with a better browsing experience by remembering your preferences and analyzing how you use our site.
            </p>
            <div className="bg-emerald-50 border border-emerald-200 rounded-xl p-6">
              <h3 className="text-lg font-semibold text-emerald-800 mb-2">Why We Use Cookies</h3>
              <p className="text-emerald-700">
                We use cookies to enhance your experience, analyze website traffic, and ensure our website functions properly. 
                All cookies are used in accordance with applicable privacy laws.
              </p>
            </div>
          </div>

          {/* Cookie Types */}
          <div className="grid gap-8 mb-8">
            {cookieTypes.map((type, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
                className="bg-white rounded-2xl shadow-lg p-8"
              >
                <div className="flex items-center mb-6">
                  <div className={`w-12 h-12 bg-${type.color}-100 rounded-xl flex items-center justify-center mr-4`}>
                    <type.icon className={`w-6 h-6 text-${type.color}-600`} />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900">{type.title}</h3>
                    <p className="text-gray-600 mt-1">{type.description}</p>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {type.examples.map((example, exampleIndex) => (
                    <div key={exampleIndex} className="flex items-center">
                      <div className={`w-2 h-2 bg-${type.color}-500 rounded-full mr-3`}></div>
                      <span className="text-gray-600">{example}</span>
                    </div>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.7 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Managing Your Cookie Preferences</h2>
            <p className="text-gray-600 leading-relaxed mb-6">
              You can control and manage cookies in various ways. Please note that removing or blocking cookies may impact your user experience.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-gray-50 rounded-xl p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Browser Settings</h3>
                <p className="text-gray-600 text-sm">
                  Most browsers allow you to control cookies through their settings. You can block or delete cookies, 
                  or set your browser to notify you when cookies are being used.
                </p>
              </div>
              <div className="bg-gray-50 rounded-xl p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Third-Party Tools</h3>
                <p className="text-gray-600 text-sm">
                  You can opt-out of analytics cookies through tools like Google Analytics Opt-out Browser Add-on 
                  or other privacy-focused browser extensions.
                </p>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            className="bg-white rounded-2xl shadow-lg p-8"
          >
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Contact Us</h2>
            <p className="text-gray-600 leading-relaxed">
              If you have any questions about our use of cookies or this Cookie Policy, please contact us through our contact form. 
              We're here to help you understand how we protect your privacy.
            </p>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};

export default CookiePolicy;
