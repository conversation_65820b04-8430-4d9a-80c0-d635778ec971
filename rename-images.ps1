# Comprehensive Image Renaming Script
# This script renames images AND updates code references to prevent breaking the site

$imageDir = "public/images"

Write-Host "=== COMPREHENSIVE IMAGE RENAMING TOOL ===" -ForegroundColor Cyan
Write-Host "This script will rename images AND update all code references" -ForegroundColor Yellow
Write-Host ""

# Define the systematic renaming based on current code usage
$renameMap = @{
    # Hero section image (keep descriptive name)
    "Herosection.jpeg" = "hero-main-product.jpeg"

    # Home page featured products (3 products)
    "WhatsApp Image 2025-06-04 at 5.58.20 PM (1).jpeg" = "product-01-dish-wash.jpeg"
    "WhatsApp Image 2025-06-04 at 5.58.20 PM (2).jpeg" = "product-02-cloth-wash.jpeg"
    "WhatsApp Image 2025-06-04 at 5.58.20 PM.jpeg" = "product-03-toilet-cleaner.jpeg"

    # Products page additional items
    "WhatsApp Image 2025-06-04 at 5.58.21 PM (1).jpeg" = "product-04-glass-cleaner.jpeg"
    "WhatsApp Image 2025-06-04 at 5.58.21 PM.jpeg" = "product-05-bathroom-cleaner.jpeg"
    "WhatsApp Image 2025-06-04 at 5.58.22 PM (1).jpeg" = "product-06-floor-cleaner.jpeg"
    "WhatsApp Image 2025-06-04 at 5.58.22 PM.jpeg" = "product-07-surface-cleaner.jpeg"
    "WhatsApp Image 2025-06-04 at 5.58.23 PM.jpeg" = "product-08-detergent-powder.jpeg"
    "WhatsApp Image 2025-06-04 at 5.58.24 PM.jpeg" = "product-09-neem-soap.jpeg"

    # Owner photo
    "WhatsApp Image 2025-06-04 at 5.58.28 PM.jpeg" = "owner-smita-godhani.jpeg"
}

# Code files that need to be updated
$codeFiles = @(
    "src/components/Hero.tsx",
    "src/pages/Home.tsx",
    "src/pages/Products.tsx"
)

Write-Host ""
Write-Host "Current Image Mapping Plan:" -ForegroundColor Green
Write-Host "===========================" -ForegroundColor Green
foreach ($oldName in $renameMap.Keys) {
    Write-Host "• $oldName" -ForegroundColor Gray
    Write-Host "  → $($renameMap[$oldName])" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "Files to be updated:" -ForegroundColor Yellow
foreach ($file in $codeFiles) {
    Write-Host "• $file" -ForegroundColor Gray
}

Write-Host ""
Write-Host "This will:" -ForegroundColor Magenta
Write-Host "1. Rename all image files to descriptive names" -ForegroundColor White
Write-Host "2. Update all code references to use new names" -ForegroundColor White
Write-Host "3. Ensure your website continues to work perfectly" -ForegroundColor White
Write-Host ""

$confirm = Read-Host "Proceed with renaming? (y/N)"
if ($confirm -ne 'y' -and $confirm -ne 'Y') {
    Write-Host "Operation cancelled." -ForegroundColor Yellow
    exit
}

# Function to update code references
function Update-CodeReferences {
    param(
        [hashtable]$RenameMap,
        [array]$CodeFiles
    )

    Write-Host ""
    Write-Host "Updating code references..." -ForegroundColor Green

    foreach ($file in $CodeFiles) {
        if (Test-Path $file) {
            Write-Host "Processing: $file" -ForegroundColor Cyan
            $content = Get-Content $file -Raw
            $updated = $false

            foreach ($oldName in $RenameMap.Keys) {
                $newName = $RenameMap[$oldName]
                $oldPath = "/images/$oldName"
                $newPath = "/images/$newName"

                if ($content -match [regex]::Escape($oldPath)) {
                    $content = $content -replace [regex]::Escape($oldPath), $newPath
                    $updated = $true
                    Write-Host "  ✓ Updated: $oldPath → $newPath" -ForegroundColor Green
                }
            }

            if ($updated) {
                Set-Content $file -Value $content -NoNewline
                Write-Host "  File updated successfully!" -ForegroundColor Green
            } else {
                Write-Host "  No changes needed" -ForegroundColor Gray
            }
        } else {
            Write-Host "  ✗ File not found: $file" -ForegroundColor Red
        }
    }
}

# Function to rename image files
function Rename-ImageFiles {
    param(
        [hashtable]$RenameMap
    )

    Write-Host ""
    Write-Host "Renaming image files..." -ForegroundColor Green
    $renamed = 0
    $skipped = 0

    foreach ($oldName in $RenameMap.Keys) {
        $newName = $RenameMap[$oldName]
        $oldPath = Join-Path $imageDir $oldName
        $newPath = Join-Path $imageDir $newName

        if (Test-Path $oldPath) {
            if (Test-Path $newPath) {
                Write-Host "  ⚠ Warning: $newName already exists. Skipping $oldName" -ForegroundColor Yellow
                $skipped++
            } else {
                try {
                    Rename-Item -Path $oldPath -NewName $newName -ErrorAction Stop
                    Write-Host "  ✓ Renamed: $oldName → $newName" -ForegroundColor Green
                    $renamed++
                } catch {
                    Write-Host "  ✗ Error renaming $oldName : $($_.Exception.Message)" -ForegroundColor Red
                }
            }
        } else {
            Write-Host "  ✗ File not found: $oldName" -ForegroundColor Red
        }
    }

    Write-Host ""
    Write-Host "Image Rename Summary:" -ForegroundColor Cyan
    Write-Host "- Successfully renamed: $renamed files" -ForegroundColor Green
    Write-Host "- Skipped: $skipped files" -ForegroundColor Yellow
}

# Execute the renaming process
Write-Host ""
Write-Host "=== STARTING COMPREHENSIVE RENAME PROCESS ===" -ForegroundColor Magenta

# Step 1: Update code references first (safer approach)
Update-CodeReferences -RenameMap $renameMap -CodeFiles $codeFiles

# Step 2: Rename the actual image files
Rename-ImageFiles -RenameMap $renameMap

Write-Host ""
Write-Host "=== PROCESS COMPLETED ===" -ForegroundColor Magenta
Write-Host "✓ All image files have been renamed with descriptive names" -ForegroundColor Green
Write-Host "✓ All code references have been updated" -ForegroundColor Green
Write-Host "✓ Your website should continue to work perfectly" -ForegroundColor Green
Write-Host ""
Write-Host "New image names follow the pattern:" -ForegroundColor Cyan
Write-Host "• hero-main-product.jpeg (for hero section)" -ForegroundColor White
Write-Host "• product-01-dish-wash.jpeg, product-02-cloth-wash.jpeg, etc." -ForegroundColor White
Write-Host "• owner-smita-godhani.jpeg (for owner photo)" -ForegroundColor White
}
