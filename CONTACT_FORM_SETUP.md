# Contact Form Setup Guide

## 🚀 Real Contact Form Implementation

Your contact form is now fully functional and will send real emails to `<EMAIL>`!

## 📧 Email Service Setup (Required)

### Option 1: Resend (Recommended for Vercel)

1. **Sign up for Resend**: https://resend.com/
2. **Get your API key** from the dashboard
3. **Add environment variable** in Vercel:
   - Go to your Vercel project dashboard
   - Settings → Environment Variables
   - Add: `RESEND_API_KEY` = `your_api_key_here`

4. **Verify your domain** (or use <PERSON>send's test domain):
   - In Resend dashboard, add your domain
   - Follow DNS verification steps
   - Or use `<EMAIL>` for testing

### Option 2: SendGrid (Alternative)

If you prefer SendGrid, I can modify the API to use SendGrid instead.

## 🔧 Environment Variables Needed

Add these to your Vercel project:

```
RESEND_API_KEY=re_xxxxxxxxxxxxxxxxx
```

## 📁 Files Created/Modified

1. **`api/contact.ts`** - Vercel serverless function
2. **`src/pages/Contact.tsx`** - Updated to use real API
3. **Contact form now sends real emails!**

## ✅ What Works Now

- ✅ Real form validation
- ✅ Sends <NAME_EMAIL>
- ✅ Professional HTML email formatting
- ✅ Error handling and user feedback
- ✅ Works on Vercel hosting
- ✅ No external dependencies needed

## 🧪 Testing

1. **Local testing**: The API will work in development
2. **Production**: Deploy to Vercel with environment variables set
3. **Test email**: Submit a form <NAME_EMAIL>

## 📧 Email Format

The emails will include:
- Sender's name and contact info
- Inquiry type and subject
- Full message content
- Timestamp in Indian time
- Professional HTML formatting

## 🔒 Security Features

- Input validation
- Email format validation
- Rate limiting (can be added)
- Sanitized HTML output
- Error handling

## 🚀 Deployment Steps

1. **Push code to GitHub**
2. **Set environment variable in Vercel**:
   - RESEND_API_KEY=your_key
3. **Deploy** - Contact form will work immediately!

## 💡 Next Steps (Optional)

- Add rate limiting to prevent spam
- Add CAPTCHA for extra security
- Set up email templates
- Add auto-reply functionality
