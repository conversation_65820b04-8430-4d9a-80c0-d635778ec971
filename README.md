# <PERSON><PERSON> - Phoenix Klein

Eco-friendly cleaning products website built with React, TypeScript, and Tailwind CSS.

## Getting Started

### Prerequisites

- Node.js (v16 or later)
- npm or yarn

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```
3. Start the development server:
   ```bash
   npm run dev
   ```

## Deployment

This project is configured for deployment on Vercel. To deploy:

1. Push your code to a GitHub, GitLab, or Bitbucket repository
2. Go to [Vercel](https://vercel.com) and sign in with your Git provider
3. Click "New Project"
4. Import your repository
5. Vercel will automatically detect the Vite + React project and apply the correct settings
6. Click "Deploy"

## Environment Variables

No environment variables are required for the basic setup.

## Built With

- [React](https://reactjs.org/)
- [TypeScript](https://www.typescriptlang.org/)
- [Vite](https://vitejs.dev/)
- [Tailwind CSS](https://tailwindcss.com/)
- [React Router](https://reactrouter.com/)
- [Lucide Icons](https://lucide.dev/)
