import { useState } from 'react';
import { motion } from 'framer-motion';
import { Mail, Phone, MapPin, Clock, Send, CheckCircle, User, Building2, Globe, Heart } from 'lucide-react';
import { useSEO } from '../hooks/useSEO';

const Contact = () => {
  // SEO optimization for Contact page
  useSEO({
    title: 'Contact Phillipina Green | Get in Touch - Eco-Friendly Cleaning Products',
    description: 'Contact Phillipina Green for inquiries about our eco-friendly cleaning products. Get support, find outlets, or learn about our women empowerment mission. Phone: +91-7016480320',
    keywords: 'contact phillipina green, eco-friendly cleaning support, customer service, product inquiries, women empowerment, natural cleaners contact',
    canonical: 'https://phillipinagreenliquid.com/contact',
    ogTitle: 'Contact Phillipina Green | Customer Support & Inquiries',
    ogDescription: 'Get in touch with our team for product support, outlet information, or partnership opportunities. We\'re here to help with all your eco-friendly cleaning needs.',
    ogImage: 'https://phillipinagreenliquid.com/images/herosection2.png',
    structuredData: {
      "@context": "https://schema.org",
      "@type": "ContactPage",
      "name": "Contact Phillipina Green",
      "description": "Contact page for Phillipina Green eco-friendly cleaning products",
      "url": "https://phillipinagreenliquid.com/contact",
      "mainEntity": {
        "@type": "Organization",
        "name": "Phillipina Green",
        "contactPoint": [
          {
            "@type": "ContactPoint",
            "telephone": "+91-7016480320",
            "contactType": "customer service",
            "email": "<EMAIL>",
            "availableLanguage": ["English", "Hindi", "Gujarati"]
          }
        ]
      }
    }
  });

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
    inquiryType: 'general'
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (response.ok) {
        setIsSubmitted(true);
        // Clear form data but keep success message visible
        setFormData({
          name: '',
          email: '',
          phone: '',
          subject: '',
          message: '',
          inquiryType: 'general'
        });
      } else {
        // Handle error
        alert(`Error: ${result.error || 'Failed to send message'}`);
      }
    } catch (error) {
      console.error('Form submission error:', error);
      alert('Failed to send message. Please try again or contact us directly.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-emerald-100">
      {/* Hero Section */}
      <div className="relative bg-gradient-to-r from-emerald-800 to-emerald-600 text-white py-20 overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>

        {/* Floating Particles */}
        <div className="absolute inset-0">
          {[...Array(20)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-white/20 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [-20, -100, -20],
                opacity: [0, 1, 0],
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="inline-flex items-center px-4 py-2 bg-emerald-500/20 rounded-full text-emerald-100 text-sm font-medium mb-6">
            <Heart className="w-4 h-4 mr-2" />
            Women Strong, Country Strong
          </div>
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            Let's Connect
          </h1>
          <p className="text-xl text-emerald-100 max-w-2xl mx-auto mb-8">
            Have questions about our eco-friendly products or interested in joining our mission?
            We're here to help you make the switch to sustainable cleaning solutions.
          </p>
          <div className="flex items-center justify-center space-x-8 text-emerald-200">
            <div className="flex items-center">
              <Globe className="w-5 h-5 mr-2" />
              <span>100% Indian Made</span>
            </div>
            <div className="flex items-center">
              <Building2 className="w-5 h-5 mr-2" />
              <span>Gujarat, India</span>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
          {/* Left Column - Contact Form + Quick Contact Cards */}
          <div className="space-y-6">
            {/* Contact Form */}
            <div className="bg-white rounded-2xl p-6 shadow-xl">
            <div className="text-center mb-5">
              <h2 className="text-xl font-bold text-gray-900 mb-2">Send us a Message</h2>
              <p className="text-gray-600 text-sm">We'd love to hear from you and will respond as soon as possible.</p>
            </div>

            {isSubmitted ? (
              <div className="text-center py-8">
                <CheckCircle className="w-12 h-12 text-emerald-600 mx-auto mb-3" />
                <h3 className="text-xl font-bold text-gray-900 mb-2">Message Sent Successfully!</h3>
                <p className="text-gray-600 mb-3">Thank you for contacting Phillipina Green. Your message has been sent to our team.</p>
                <div className="bg-emerald-50 border border-emerald-200 rounded-lg p-4 text-sm text-emerald-800 mb-4">
                  <p><strong>What happens next?</strong></p>
                  <p className="mt-1">• We'll review your message within 24 hours</p>
                  <p>• Our team will respond via email or phone</p>
                  <p>• For urgent matters, call us at <a href="tel:+************" className="text-emerald-600 hover:underline font-medium">+91 75378 30000</a></p>
                </div>
                <button
                  onClick={() => setIsSubmitted(false)}
                  className="bg-emerald-600 text-white py-2 px-6 rounded-xl font-medium hover:bg-emerald-700 transition-all duration-300 transform hover:scale-105"
                >
                  Send Another Message
                </button>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                      Full Name *
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      required
                      value={formData.name}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                      placeholder="Your full name"
                    />
                  </div>

                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                      placeholder="+91 XXXXX XXXXX"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    required
                    value={formData.email}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <label htmlFor="inquiryType" className="block text-sm font-medium text-gray-700 mb-2">
                    Inquiry Type
                  </label>
                  <select
                    id="inquiryType"
                    name="inquiryType"
                    value={formData.inquiryType}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                  >
                    <option value="general">General Inquiry</option>
                    <option value="products">Product Information</option>
                    <option value="business">Business Opportunity</option>
                    <option value="support">Customer Support</option>
                    <option value="partnership">Partnership</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">
                    Subject *
                  </label>
                  <input
                    type="text"
                    id="subject"
                    name="subject"
                    required
                    value={formData.subject}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                    placeholder="How can we help you?"
                  />
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                    Message *
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    required
                    rows={3}
                    value={formData.message}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors resize-none"
                    placeholder="Tell us more about your inquiry..."
                  ></textarea>
                </div>

                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-emerald-600 text-white py-3 px-6 rounded-xl font-medium hover:bg-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 transform hover:scale-105 flex items-center justify-center"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      Sending...
                    </>
                  ) : (
                    <>
                      <Send className="w-5 h-5 mr-2" />
                      Send Message
                    </>
                  )}
                </button>
              </form>
            )}
            </div>


          </div>

          {/* Right Column - Contact Information */}
          <div className="space-y-6">
            {/* Company Info Card */}
            <div className="bg-gradient-to-br from-emerald-600 to-emerald-700 rounded-2xl p-8 text-white h-fit">
              <div className="text-center mb-8">
                <div className="w-16 h-16 bg-emerald-500/30 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Building2 className="w-8 h-8 text-emerald-100" />
                </div>
                <h2 className="text-2xl font-bold mb-3">Phoenix Klein Laboratories</h2>
                <p className="text-emerald-100 text-lg">Your trusted partner in eco-friendly cleaning solutions</p>
              </div>

              <div className="space-y-6 flex-1">
                <div className="bg-emerald-500/20 rounded-xl p-4">
                  <div className="flex items-start">
                    <MapPin className="w-6 h-6 text-emerald-200 mt-1 flex-shrink-0" />
                    <div className="ml-4">
                      <h3 className="font-semibold text-white mb-2">Factory & Office Address</h3>
                      <p className="text-emerald-100 leading-relaxed">
                        Plot No. G-820/A, Lodhika GIDC<br />
                        Road D-2, Metoda (Rajkot) - 360021<br />
                        Gujarat, India
                      </p>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="bg-emerald-500/20 rounded-xl p-4">
                    <div className="flex items-start">
                      <Phone className="w-6 h-6 text-emerald-200 mt-1 flex-shrink-0" />
                      <div className="ml-4">
                        <h3 className="font-semibold text-white mb-2">Phone</h3>
                        <p className="text-emerald-100">
                          <a href="tel:+************" className="hover:text-white transition-colors">
                            +91 75378 30000
                          </a>
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-emerald-500/20 rounded-xl p-4">
                    <div className="flex items-start">
                      <Mail className="w-6 h-6 text-emerald-200 mt-1 flex-shrink-0" />
                      <div className="ml-4">
                        <h3 className="font-semibold text-white mb-2">Email</h3>
                        <p className="text-emerald-100">
                          <a href="mailto:<EMAIL>" className="hover:text-white transition-colors">
                            <EMAIL>
                          </a>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-emerald-500/20 rounded-xl p-4">
                  <div className="flex items-start">
                    <Clock className="w-6 h-6 text-emerald-200 mt-1 flex-shrink-0" />
                    <div className="ml-4">
                      <h3 className="font-semibold text-white mb-2">Business Hours</h3>
                      <p className="text-emerald-100 leading-relaxed">
                        Monday - Friday: 9:00 AM - 6:00 PM<br />
                        Saturday: 9:00 AM - 2:00 PM<br />
                        Sunday: Closed
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>




          </div>
        </div>



        {/* Call to Action */}
        <div className="mt-8 text-center">
          <div className="bg-gradient-to-r from-emerald-600 to-emerald-700 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">Ready to Make the Switch?</h3>
            <p className="text-emerald-100 mb-6 max-w-2xl mx-auto">
              Join thousands of families who have already made the switch to eco-friendly cleaning solutions.
              Contact us today to learn more about our products and business opportunities.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="tel:+************"
                className="bg-white text-emerald-600 px-8 py-3 rounded-xl font-medium hover:bg-emerald-50 transition-colors inline-flex items-center justify-center"
              >
                <Phone className="w-5 h-5 mr-2" />
                Call Now
              </a>
              <a
                href="mailto:<EMAIL>"
                className="border-2 border-white text-white px-8 py-3 rounded-xl font-medium hover:bg-white hover:text-emerald-600 transition-colors inline-flex items-center justify-center"
              >
                <Mail className="w-5 h-5 mr-2" />
                Send Email
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Contact;