export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      error: 'Method not allowed', 
      method: req.method,
      debug: 'Only POST requests are allowed'
    });
  }

  try {
    const { email } = req.body;

    // Validate required fields
    if (!email) {
      return res.status(400).json({ error: 'Email address is required' });
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({ error: 'Invalid email format' });
    }

    // Create email content for notification
    const emailContent = `
New Newsletter Subscription from Phillipina Green Website

Email: ${email}

---
Subscribed from: Phillipina Green Newsletter Form
Time: ${new Date().toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' })}
    `.trim();

    // Using Resend (recommended for Vercel)
    const RESEND_API_KEY = process.env.RESEND_API_KEY;
    
    if (!RESEND_API_KEY) {
      console.error('RESEND_API_KEY not found in environment variables');
      return res.status(500).json({ 
        error: 'Email service not configured',
        debug: 'RESEND_API_KEY environment variable is missing',
        env_check: process.env.NODE_ENV
      });
    }

    // Send notification email to admin
    const emailResponse = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${RESEND_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        from: '<EMAIL>', // Using Resend's test domain - change to your verified domain later
        to: ['<EMAIL>'],
        subject: `New Newsletter Subscription - ${email}`,
        text: emailContent,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #059669;">New Newsletter Subscription</h2>
            <div style="background: #f0fdf4; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <p><strong>Email:</strong> <a href="mailto:${email}">${email}</a></p>
            </div>
            <div style="margin-top: 20px; padding: 15px; background: #f9fafb; border-radius: 8px; font-size: 12px; color: #6b7280;">
              <p>Subscribed from: Phillipina Green Newsletter Form</p>
              <p>Time: ${new Date().toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' })}</p>
            </div>
          </div>
        `,
      }),
    });

    if (!emailResponse.ok) {
      const errorData = await emailResponse.text();
      console.error('Resend API error:', errorData);
      return res.status(500).json({ error: 'Failed to send notification email' });
    }

    const result = await emailResponse.json();
    console.log('Newsletter subscription notification sent:', result);

    // Add subscriber to Resend audience/contacts
    try {
      const audienceResponse = await fetch('https://api.resend.com/audiences', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${RESEND_API_KEY}`,
          'Content-Type': 'application/json',
        },
      });

      if (audienceResponse.ok) {
        const audiences = await audienceResponse.json();

        // Use the first audience or create one if none exists
        let audienceId = null;
        if (audiences.data && audiences.data.length > 0) {
          audienceId = audiences.data[0].id;
        } else {
          // Create a new audience if none exists
          const createAudienceResponse = await fetch('https://api.resend.com/audiences', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${RESEND_API_KEY}`,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              name: 'Phillipina Green Newsletter Subscribers'
            }),
          });

          if (createAudienceResponse.ok) {
            const newAudience = await createAudienceResponse.json();
            audienceId = newAudience.id;
          }
        }

        // Add contact to audience
        if (audienceId) {
          const addContactResponse = await fetch(`https://api.resend.com/audiences/${audienceId}/contacts`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${RESEND_API_KEY}`,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              email: email,
              first_name: '', // We don't collect name in newsletter signup
              last_name: '',
              unsubscribed: false
            }),
          });

          if (addContactResponse.ok) {
            console.log('Contact added to Resend audience successfully');
          } else {
            const contactError = await addContactResponse.text();
            console.log('Failed to add contact to audience:', contactError);
          }
        }
      }
    } catch (audienceError) {
      console.log('Audience management error (non-critical):', audienceError.message);
      // Don't fail the main request if audience management fails
    }

    // Send welcome email to subscriber
    const welcomeEmailResponse = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${RESEND_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        from: '<EMAIL>',
        to: [email],
        subject: 'Welcome to Phillipina Green Newsletter!',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #059669 0%, #047857 100%); color: white; border-radius: 12px; overflow: hidden;">
            <div style="padding: 40px 30px; text-align: center;">
              <h1 style="margin: 0 0 20px 0; font-size: 28px; font-weight: bold;">Welcome to Phillipina Green!</h1>
              <p style="margin: 0 0 30px 0; font-size: 16px; opacity: 0.9;">Thank you for subscribing to our newsletter. You'll now receive the latest updates, eco-friendly tips, and exclusive offers.</p>
              
              <div style="background: rgba(255,255,255,0.1); padding: 25px; border-radius: 8px; margin: 30px 0;">
                <h3 style="margin: 0 0 15px 0; color: #a7f3d0;">What to expect:</h3>
                <ul style="list-style: none; padding: 0; margin: 0; text-align: left;">
                  <li style="padding: 5px 0; opacity: 0.9;">🌱 Eco-friendly cleaning tips</li>
                  <li style="padding: 5px 0; opacity: 0.9;">📢 Product updates and launches</li>
                  <li style="padding: 5px 0; opacity: 0.9;">💚 Exclusive offers and discounts</li>
                  <li style="padding: 5px 0; opacity: 0.9;">🏪 Outlet locations and events</li>
                </ul>
              </div>
              
              <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid rgba(255,255,255,0.2); font-size: 14px; opacity: 0.8;">
                <p style="margin: 0;">Follow us for daily updates:</p>
                <p style="margin: 10px 0 0 0;">
                  <a href="https://phillipinagreenliquid.com" style="color: #a7f3d0; text-decoration: none;">Visit our website</a> | 
                  <a href="tel:+************" style="color: #a7f3d0; text-decoration: none;">Call us: +91 75378 30000</a>
                </p>
              </div>
            </div>
          </div>
        `,
      }),
    });

    // Don't fail the main request if welcome email fails
    if (welcomeEmailResponse.ok) {
      console.log('Welcome email sent successfully');
    } else {
      console.log('Welcome email failed, but subscription recorded');
    }

    // Return success response
    return res.status(200).json({ 
      success: true, 
      message: 'Successfully subscribed to newsletter',
      email: email
    });

  } catch (error) {
    console.error('Newsletter subscription error:', error);
    return res.status(500).json({ 
      error: 'Internal server error',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}
