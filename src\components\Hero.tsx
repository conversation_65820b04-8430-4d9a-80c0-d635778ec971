import { useNavigate } from 'react-router-dom';
import { Leaf, Droplets, Award, Globe, ArrowRight, Star, Shield } from 'lucide-react';

const Hero = () => {
  const navigate = useNavigate();

  return (
    <div className="relative overflow-hidden bg-gradient-to-br from-emerald-50 via-white to-emerald-50/50 min-h-screen flex items-center">
      {/* Static Decorative elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-0 left-0 w-full h-64 bg-[url('/images/hero-main-product.jpeg')] bg-cover opacity-5" />
        <div className="absolute top-10 right-10 w-96 h-96 rounded-full bg-gradient-to-br from-emerald-400/30 to-emerald-600/20 mix-blend-multiply filter blur-3xl" />
        <div className="absolute -bottom-20 -left-20 w-96 h-96 rounded-full bg-gradient-to-tr from-emerald-600/20 to-emerald-400/10 mix-blend-multiply filter blur-3xl" />

        {/* Static particles */}
        {[...Array(6)].map((_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-emerald-400/40 rounded-full"
            style={{
              left: `${20 + i * 15}%`,
              top: `${30 + (i % 2) * 40}%`,
            }}
          />
        ))}
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 md:py-28">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          <div className="space-y-8">
            <div className="flex items-center space-x-2">
              <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-gradient-to-r from-emerald-100 to-emerald-50 text-emerald-800 border border-emerald-200 shadow-sm hover:scale-105 transition-transform">
                <Globe size={16} className="mr-2" />
                <span className="mr-2 px-2 py-0.5 bg-emerald-600 text-white rounded-full text-xs">New</span>
                <span className="text-emerald-700 font-semibold">🇮🇳 100% Indian-made</span>
              </span>
            </div>

            <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold leading-tight">
              <span className="block text-gray-900 mb-2">
                Naturally Clean.
              </span>
              <span className="block bg-gradient-to-r from-emerald-600 to-emerald-500 bg-clip-text text-transparent">
                Brilliantly Green.
              </span>
            </h1>

            <p className="text-xl md:text-2xl text-gray-600 max-w-2xl leading-relaxed">
              Experience the power of nature with <span className="font-semibold text-emerald-700">Phillipina Green</span>.
              Our 100% Indian-made home cleaning products empower women with self-respectful livelihood opportunities.
            </p>

            <div className="flex flex-wrap gap-4">
              {[
                { icon: Leaf, label: "100% Plant-based", color: "emerald" },
                { icon: Droplets, label: "Biodegradable", color: "emerald" },
                { icon: Shield, label: "Safe Formula", color: "emerald" }
              ].map((item, index) => (
                <div
                  key={index}
                  className="flex items-center px-4 py-3 bg-white/90 backdrop-blur-sm rounded-full shadow-lg border border-emerald-100 hover:scale-105 hover:-translate-y-0.5 transition-transform"
                >
                  <item.icon size={18} className="text-emerald-600 mr-3" />
                  <span className="text-sm font-medium text-gray-700">{item.label}</span>
                </div>
              ))}
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <button
                onClick={() => navigate('/products')}
                className="group px-8 py-4 bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 text-white font-semibold rounded-xl shadow-lg shadow-emerald-500/25 hover:shadow-xl hover:shadow-emerald-500/40 transition-all duration-300 hover:scale-102 hover:-translate-y-0.5"
              >
                <span className="flex items-center">
                  Explore Products
                  <ArrowRight className="ml-2 transform group-hover:translate-x-1 transition-transform" size={18} />
                </span>
              </button>
              <button
                onClick={() => navigate('/contact')}
                className="group px-8 py-4 bg-white/90 backdrop-blur-sm hover:bg-white text-emerald-600 hover:text-emerald-700 font-semibold rounded-xl shadow-lg border-2 border-emerald-600 hover:border-emerald-700 transition-all duration-300 hover:scale-102 hover:-translate-y-0.5"
              >
                <span className="flex items-center">
                  Contact Us
                  <ArrowRight className="ml-2 transform group-hover:translate-x-1 transition-transform" size={18} />
                </span>
              </button>
            </div>

            <div className="pt-6">
              <div className="inline-flex items-center bg-white/90 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-emerald-100 hover:scale-102 hover:shadow-xl transition-all duration-300">
                <div className="flex -space-x-2 mr-6">
                  {[1, 2, 3].map((i) => (
                    <img
                      key={i}
                      className="w-12 h-12 rounded-full border-3 border-white object-cover ring-2 ring-emerald-100 hover:scale-110 transition-transform"
                      src={`/images/hero-main-product.jpeg`}
                      alt={`Customer ${i}`}
                    />
                  ))}
                  <div className="w-12 h-12 rounded-full bg-gradient-to-br from-emerald-500 to-emerald-600 border-3 border-white flex items-center justify-center ring-2 ring-emerald-100 hover:scale-110 transition-transform">
                    <span className="text-sm font-bold text-white">2K+</span>
                  </div>
                </div>
                <div>
                  <div className="flex items-center mb-2">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                    ))}
                    <span className="ml-2 text-sm font-semibold text-gray-700">4.9</span>
                  </div>
                  <p className="text-sm text-gray-600 font-medium">Trusted by 2,300+ families across India</p>
                </div>
              </div>
            </div>
          </div>

          <div className="relative flex justify-center">
            <div className="relative w-full max-w-lg">
              {/* Background decorative elements */}
              <div className="absolute inset-0 bg-gradient-to-br from-emerald-300/30 to-emerald-600/30 rounded-3xl transform rotate-3 scale-105" />

              {/* Main product image */}
              <div className="relative z-10">
                <img
                  src="/images/hero-main-product.jpeg"
                  alt="Phillipina Green Detergent Bottle"
                  className="relative z-10 w-full h-auto rounded-2xl shadow-2xl hover:rotate-2 hover:scale-105 transition-transform duration-300"
                />

                {/* Static floating elements */}
                <div className="absolute -top-6 -right-6 bg-white rounded-full p-4 shadow-xl z-20 border-2 border-emerald-100 hover:scale-110 transition-transform">
                  <Leaf size={28} className="text-emerald-500" />
                </div>

                <div className="absolute -bottom-4 -left-4 bg-gradient-to-r from-emerald-500 to-emerald-600 text-white rounded-full px-6 py-3 shadow-xl z-20 border-2 border-white hover:scale-110 transition-transform">
                  <span className="text-sm font-bold">100% Eco-friendly</span>
                </div>

                {/* Additional floating badges */}
                <div className="absolute top-1/4 -left-8 bg-white rounded-full p-3 shadow-lg z-20 border border-blue-100">
                  <Droplets size={20} className="text-blue-500" />
                </div>

                <div className="absolute bottom-1/4 -right-8 bg-white rounded-full p-3 shadow-lg z-20 border border-yellow-100">
                  <Award size={20} className="text-yellow-500" />
                </div>
              </div>

              {/* Static water droplet effects */}
              <div className="absolute top-1/4 right-0 w-24 h-24 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full opacity-60 blur-lg" />
              <div className="absolute bottom-1/3 left-0 w-20 h-20 bg-gradient-to-br from-emerald-100 to-emerald-200 rounded-full opacity-50 blur-lg" />
            </div>
          </div>
        </div>
      </div>

      {/* Wave decoration at bottom */}
      <div className="absolute bottom-0 left-0 right-0">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 120" className="w-full h-auto">
          <path 
            fill="#f0fdf4" 
            fillOpacity="1" 
            d="M0,64L60,64C120,64,240,64,360,53.3C480,43,600,21,720,32C840,43,960,85,1080,90.7C1200,96,1320,64,1380,48L1440,32L1440,120L1380,120C1320,120,1200,120,1080,120C960,120,840,120,720,120C600,120,480,120,360,120C240,120,120,120,60,120L0,120Z"
          ></path>
        </svg>
      </div>
    </div>
  );
};

export default Hero;