import { useEffect } from 'react';

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string;
  canonical?: string;
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  structuredData?: object;
}

export const useSEO = ({
  title,
  description,
  keywords,
  canonical,
  ogTitle,
  ogDescription,
  ogImage,
  structuredData
}: SEOProps) => {
  useEffect(() => {
    // Update document title
    if (title) {
      document.title = title;
    }

    // Update meta description
    if (description) {
      updateMetaTag('description', description);
    }

    // Update keywords
    if (keywords) {
      updateMetaTag('keywords', keywords);
    }

    // Update canonical URL
    if (canonical) {
      updateCanonical(canonical);
    }

    // Update Open Graph tags
    if (ogTitle) {
      updateMetaProperty('og:title', ogTitle);
    }
    if (ogDescription) {
      updateMetaProperty('og:description', ogDescription);
    }
    if (ogImage) {
      updateMetaProperty('og:image', ogImage);
    }
    if (canonical) {
      updateMetaProperty('og:url', canonical);
    }

    // Update Twitter tags
    if (ogTitle) {
      updateMetaProperty('twitter:title', ogTitle);
    }
    if (ogDescription) {
      updateMetaProperty('twitter:description', ogDescription);
    }
    if (ogImage) {
      updateMetaProperty('twitter:image', ogImage);
    }

    // Add structured data
    if (structuredData) {
      addStructuredData(structuredData);
    }

    // Cleanup function to reset to default values when component unmounts
    return () => {
      document.title = 'Phillipina Green - Premium Eco-Friendly Cleaning Products | Natural & Safe Home Cleaners';
      updateMetaTag('description', 'Discover Phillipina Green\'s premium eco-friendly cleaning products. Natural, safe, and effective solutions for home cleaning. Empowering women while protecting your family and environment. Shop dish wash, cloth wash, toilet cleaners & more.');
    };
  }, [title, description, keywords, canonical, ogTitle, ogDescription, ogImage, structuredData]);
};

const updateMetaTag = (name: string, content: string) => {
  let meta = document.querySelector(`meta[name="${name}"]`) as HTMLMetaElement;
  if (!meta) {
    meta = document.createElement('meta');
    meta.name = name;
    document.head.appendChild(meta);
  }
  meta.content = content;
};

const updateMetaProperty = (property: string, content: string) => {
  let meta = document.querySelector(`meta[property="${property}"]`) as HTMLMetaElement;
  if (!meta) {
    meta = document.createElement('meta');
    meta.setAttribute('property', property);
    document.head.appendChild(meta);
  }
  meta.content = content;
};

const updateCanonical = (url: string) => {
  let link = document.querySelector('link[rel="canonical"]') as HTMLLinkElement;
  if (!link) {
    link = document.createElement('link');
    link.rel = 'canonical';
    document.head.appendChild(link);
  }
  link.href = url;
};

const addStructuredData = (data: object) => {
  // Remove existing structured data for this page
  const existingScript = document.querySelector('script[data-page-structured-data]');
  if (existingScript) {
    existingScript.remove();
  }

  // Add new structured data
  const script = document.createElement('script');
  script.type = 'application/ld+json';
  script.setAttribute('data-page-structured-data', 'true');
  script.textContent = JSON.stringify(data);
  document.head.appendChild(script);
};
