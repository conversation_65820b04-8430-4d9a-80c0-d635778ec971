import React from 'react';
import { Leaf, Recycle, Crop as Drop, TreePine } from 'lucide-react';

const Sustainability = () => {
  return (
    <div className="bg-white">
      {/* Hero Section */}
      <div className="relative bg-emerald-800 text-white py-24">
        <div className="absolute inset-0">
          <img
            src="https://images.pexels.com/photos/3735218/pexels-photo-3735218.jpeg"
            alt="Sustainability"
            className="w-full h-full object-cover opacity-20"
          />
        </div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">Our Commitment to Sustainability</h1>
          <p className="text-lg text-emerald-100 max-w-2xl mx-auto">
            At Phillipina Green, sustainability isn't just a buzzword—it's our foundation.
            Discover how we're making a difference for our planet.
          </p>
        </div>
      </div>

      {/* Environmental Impact */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
          <div>
            <h2 className="text-3xl font-bold text-gray-900 mb-6">Our Environmental Impact</h2>
            <div className="space-y-4">
              <p className="text-gray-600">
                Every product we create is designed with the environment in mind. From sourcing
                to packaging, we're committed to reducing our ecological footprint.
              </p>
              <ul className="space-y-4">
                <li className="flex items-start">
                  <Leaf className="w-6 h-6 text-emerald-600 mt-1" />
                  <span className="ml-4 text-gray-600">
                    100% biodegradable ingredients
                  </span>
                </li>
                <li className="flex items-start">
                  <Recycle className="w-6 h-6 text-emerald-600 mt-1" />
                  <span className="ml-4 text-gray-600">
                    Recyclable packaging made from post-consumer materials
                  </span>
                </li>
                <li className="flex items-start">
                  <Drop className="w-6 h-6 text-emerald-600 mt-1" />
                  <span className="ml-4 text-gray-600">
                    Water-saving formula
                  </span>
                </li>
              </ul>
            </div>
          </div>
          <div className="relative">
            <img
              src="https://images.pexels.com/photos/3735217/pexels-photo-3735217.jpeg"
              alt="Environmental Impact"
              className="rounded-lg shadow-xl"
            />
          </div>
        </div>
      </div>

      {/* Initiatives */}
      <div className="bg-emerald-50 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
            Our Green Initiatives
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                title: "Tree Planting Program",
                description: "For every 100 products sold, we plant one tree in partnership with local communities",
                icon: <TreePine className="w-12 h-12" />
              },
              {
                title: "Zero Waste Production",
                description: "Our manufacturing process is designed to minimize waste and maximize resource efficiency",
                icon: <Recycle className="w-12 h-12" />
              },
              {
                title: "Clean Water Initiative",
                description: "Supporting programs to keep Philippine waters clean and safe",
                icon: <Drop className="w-12 h-12" />
              }
            ].map((initiative, index) => (
              <div key={index} className="bg-white rounded-lg p-6 shadow-lg">
                <div className="text-emerald-600 mb-4">
                  {initiative.icon}
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {initiative.title}
                </h3>
                <p className="text-gray-600">{initiative.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Certifications */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
          Our Certifications
        </h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
          {[
            "USDA Organic",
            "EcoCert",
            "Cruelty Free",
            "Philippine Green Seal"
          ].map((cert, index) => (
            <div key={index} className="text-center">
              <div className="w-24 h-24 mx-auto bg-emerald-100 rounded-full flex items-center justify-center mb-4">
                <Leaf className="w-12 h-12 text-emerald-600" />
              </div>
              <h3 className="font-medium text-gray-900">{cert}</h3>
            </div>
          ))}
        </div>
      </div>

      {/* Call to Action */}
      <div className="bg-emerald-800 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-6">Join Our Green Movement</h2>
          <p className="text-emerald-100 mb-8 max-w-2xl mx-auto">
            Together, we can make a difference. Switch to eco-friendly cleaning solutions
            and be part of the change.
          </p>
          <button className="bg-white text-emerald-800 px-8 py-3 rounded-lg font-medium hover:bg-emerald-50 transition-colors duration-200">
            Shop Eco-Friendly Products
          </button>
        </div>
      </div>
    </div>
  );
};

export default Sustainability;