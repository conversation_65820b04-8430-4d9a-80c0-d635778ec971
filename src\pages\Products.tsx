import { useState } from 'react';
import { motion } from 'framer-motion';
import { Star, Filter, Leaf, Award, Users, Droplets, Shield } from 'lucide-react';
import { useSEO } from '../hooks/useSEO';

const Products = () => {
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [sortBy, setSortBy] = useState('name');

  // SEO optimization for Products page
  useSEO({
    title: 'Eco-Friendly Cleaning Products | Natural Home Cleaners - Phillipina Green',
    description: 'Shop premium eco-friendly cleaning products by <PERSON><PERSON> Green. Natural dish wash, cloth wash, toilet cleaners, floor cleaners & more. Safe for family, effective cleaning, women empowerment.',
    keywords: 'eco-friendly cleaning products, natural cleaners, dish wash liquid, cloth wash, toilet cleaner, bathroom cleaner, floor cleaner, detergent powder, neem soap, safe cleaning products',
    canonical: 'https://phillipinagreenliquid.com/products',
    ogTitle: 'Premium Eco-Friendly Cleaning Products | Phillipina Green',
    ogDescription: 'Discover our complete range of natural, safe, and effective cleaning solutions. From dish wash to floor cleaners - all eco-friendly and women-empowering.',
    ogImage: 'https://phillipinagreenliquid.com/images/herosection2.png',
    structuredData: {
      "@context": "https://schema.org",
      "@type": "CollectionPage",
      "name": "Eco-Friendly Cleaning Products",
      "description": "Complete collection of premium eco-friendly cleaning products",
      "url": "https://phillipinagreenliquid.com/products",
      "mainEntity": {
        "@type": "ItemList",
        "numberOfItems": 9,
        "itemListElement": [
          {
            "@type": "Product",
            "position": 1,
            "name": "Dish Wash Liquid",
            "description": "Non-acidic, soft on hands, removes odor and bacteria",
            "image": "https://phillipinagreenliquid.com/images/dishwash-liquid.jpeg",
            "offers": {
              "@type": "Offer",
              "price": "35.00",
              "priceCurrency": "INR"
            }
          },
          {
            "@type": "Product",
            "position": 2,
            "name": "Cloth Wash Liquid",
            "description": "Non-acidic, soft on hands, removes stains without color damage",
            "image": "https://phillipinagreenliquid.com/images/cloth-wash-liquid.jpeg",
            "offers": {
              "@type": "Offer",
              "price": "70.00",
              "priceCurrency": "INR"
            }
          }
        ]
      }
    }
  });

  const products = [
    {
      id: 1,
      name: "Dish Wash Liquid",
      description: "Non-acidic, soft on hands, removes odor and bacteria",
      price: "₹35.00",
      image: "/images/dishwash-liquid.jpeg",
      rating: 4.8,
      reviews: 156,
      category: "Kitchen",
      features: ["Non-acidic formula", "Gentle on hands", "Antibacterial"]
    },
    {
      id: 2,
      name: "Cloth Wash Liquid",
      description: "Non-acidic, soft on hands, removes stains without color damage",
      price: "₹70.00",
      image: "/images/cloth-wash-liquid.jpeg",
      rating: 4.9,
      reviews: 243,
      category: "Laundry",
      features: ["Color protection", "Stain removal", "Fabric care"]
    },
    {
      id: 3,
      name: "Detergent Powder",
      description: "Non-acidic, low residue, soft on fabric and skin",
      price: "₹75.00",
      image: "/images/detergent-powder.jpeg",
      rating: 4.9,
      reviews: 178,
      category: "Laundry",
      features: ["Low residue", "Skin friendly", "Deep cleaning"]
    },
    {
      id: 4,
      name: "Bathroom Cleaner",
      description: "Non-acidic, requires less water, low foam, germicidal",
      price: "₹45.00",
      image: "/images/bathroom-cleaner.jpeg",
      rating: 4.8,
      reviews: 167,
      category: "Bathroom",
      features: ["Water efficient", "Low foam", "Antimicrobial"]
    },
    {
      id: 5,
      name: "Floor Cleaner",
      description: "Non-acidic, germicidal, use on tiles, marble, kitchen tops",
      price: "₹45.00",
      image: "/images/floor-cleaner.jpeg",
      rating: 4.7,
      reviews: 145,
      category: "Multi-Purpose",
      features: ["All floor types", "Germ protection", "Shine enhancer"]
    },
    {
      id: 6,
      name: "Surface Cleaner",
      description: "Acidic, removes old stains, ideal for dry steel/brass surfaces",
      price: "₹80.00",
      image: "/images/surface-cleaner.jpeg",
      rating: 4.8,
      reviews: 132,
      category: "Multi-Purpose",
      features: ["Heavy-duty cleaning", "Metal safe", "Stain removal"]
    },
    {
      id: 7,
      name: "Neem & Aloevera Soap",
      description: "Protects from pollution, nourishes and cleanses skin",
      price: "₹26.00",
      image: "/images/neem-alovera-soap.jpeg",
      rating: 4.8,
      reviews: 156,
      category: "Personal Care",
      features: ["Natural ingredients", "Pollution protection", "Skin nourishing"]
    },
    {
      id: 8,
      name: "Ayurvedic Petals Soap",
      description: "Natural ayurvedic ingredients with flower petals for gentle cleansing",
      price: "₹26.00",
      image: "/images/ayurvedic-petals-soap.jpeg",
      rating: 4.9,
      reviews: 134,
      category: "Personal Care",
      features: ["Ayurvedic formula", "Flower petals", "Gentle cleansing"]
    },
    {
      id: 9,
      name: "Creamy Shea Butter Soap",
      description: "Rich shea butter formula for deep moisturizing and skin care",
      price: "₹26.00",
      image: "/images/creamy-shea-butter-soap.jpeg",
      rating: 4.8,
      reviews: 112,
      category: "Personal Care",
      features: ["Shea butter", "Deep moisturizing", "Skin care"]
    }
  ];

  const categories = ['All', 'Kitchen', 'Laundry', 'Bathroom', 'Multi-Purpose', 'Personal Care'];

  const filteredProducts = products.filter(product =>
    selectedCategory === 'All' || product.category === selectedCategory
  );

  const sortedProducts = [...filteredProducts].sort((a, b) => {
    switch (sortBy) {
      case 'rating':
        return b.rating - a.rating;
      case 'reviews':
        return b.reviews - a.reviews;
      case 'category':
        return a.category.localeCompare(b.category);
      default:
        return a.name.localeCompare(b.name);
    }
  });



  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-emerald-100">
      {/* Hero Section */}
      <div className="relative bg-gradient-to-r from-emerald-800 to-emerald-600 text-white py-20 overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>

        {/* Floating Particles */}
        <div className="absolute inset-0">
          {[...Array(20)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-white/20 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [-20, -100, -20],
                opacity: [0, 1, 0],
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="inline-flex items-center px-4 py-2 bg-emerald-500/20 rounded-full text-emerald-100 text-sm font-medium mb-6">
            <Leaf className="w-4 h-4 mr-2" />
            100% Indian-Made Products
          </div>
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            Our Premium Product Range
          </h1>
          <p className="text-xl text-emerald-100 max-w-2xl mx-auto">
            Explore our complete collection of eco-friendly cleaning solutions made with care in India
          </p>
          <div className="flex items-center justify-center space-x-8 mt-8 text-emerald-200">
            <div className="flex items-center">
              <Award className="w-5 h-5 mr-2" />
              <span>Premium Quality</span>
            </div>
            <div className="flex items-center">
              <Shield className="w-5 h-5 mr-2" />
              <span>Safe & Natural</span>
            </div>
            <div className="flex items-center">
              <Droplets className="w-5 h-5 mr-2" />
              <span>Eco-Friendly</span>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Filters and Sorting */}
        <div className="bg-white rounded-2xl shadow-lg p-6 mb-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <div className="flex items-center space-x-4">
              <Filter className="w-5 h-5 text-emerald-600" />
              <span className="font-medium text-gray-900">Filter by Category:</span>
              <div className="flex flex-wrap gap-2">
                {categories.map((category) => (
                  <button
                    key={category}
                    onClick={() => setSelectedCategory(category)}
                    className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
                      selectedCategory === category
                        ? 'bg-emerald-600 text-white shadow-lg'
                        : 'bg-gray-100 text-gray-700 hover:bg-emerald-50 hover:text-emerald-700'
                    }`}
                  >
                    {category}
                  </button>
                ))}
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <span className="font-medium text-gray-900">Sort by:</span>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
              >
                <option value="name">Name (A-Z)</option>
                <option value="rating">Highest Rated</option>
                <option value="reviews">Most Reviews</option>
                <option value="category">Category</option>
              </select>
            </div>
          </div>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {sortedProducts.map((product, index) => (
            <div
              key={product.id}
              className="group bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className="relative overflow-hidden bg-white">
                <img
                  src={product.image}
                  alt={product.name}
                  className="w-full h-80 object-contain group-hover:scale-105 transition-transform duration-500 p-6"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                {/* Rating Badge */}
                <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full shadow-lg">
                  <div className="flex items-center">
                    <Star className="w-4 h-4 text-yellow-400 fill-current" />
                    <span className="ml-1 text-sm font-bold text-gray-900">{product.rating}</span>
                  </div>
                </div>

                {/* Category Badge */}
                <div className="absolute top-4 left-4 bg-emerald-600 text-white px-3 py-1 rounded-full text-xs font-medium">
                  {product.category}
                </div>
              </div>

              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-emerald-600 transition-colors">
                  {product.name}
                </h3>
                <p className="text-gray-600 mb-4 line-clamp-2">{product.description}</p>

                {/* Features */}
                <div className="flex flex-wrap gap-1 mb-4">
                  {product.features.slice(0, 3).map((feature, idx) => (
                    <span
                      key={idx}
                      className="inline-flex items-center px-2 py-1 bg-emerald-50 text-emerald-700 text-xs rounded-full"
                    >
                      <Leaf className="w-3 h-3 mr-1" />
                      {feature}
                    </span>
                  ))}
                </div>

                {/* Product Info */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center text-yellow-400">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`w-4 h-4 ${i < Math.floor(product.rating) ? 'fill-current' : 'text-gray-300'}`}
                      />
                    ))}
                    <span className="ml-2 text-sm font-medium text-gray-700">{product.rating}</span>
                  </div>
                  <span className="text-sm text-gray-500">{product.reviews} reviews</span>
                </div>

                {/* Price Display */}
                <div className="bg-emerald-50 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-emerald-600 mb-1">{product.price}</div>
                  <div className="text-sm text-emerald-700">Starting Price</div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {sortedProducts.length === 0 && (
          <div className="text-center py-16">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Filter className="w-12 h-12 text-gray-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">No products found</h3>
            <p className="text-gray-600 mb-6">Try adjusting your filters to see more products.</p>
            <button
              onClick={() => setSelectedCategory('All')}
              className="bg-emerald-600 text-white px-6 py-2 rounded-lg hover:bg-emerald-700 transition-colors"
            >
              Show All Products
            </button>
          </div>
        )}

        {/* Product Showcase Stats */}
        <div className="mt-16 bg-gradient-to-r from-emerald-600 to-emerald-700 rounded-2xl p-8 text-white">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold mb-2">Why Choose Phillipina Green?</h3>
            <p className="text-emerald-100">Committed to quality, sustainability, and your family's well-being</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold mb-2">{products.length}</div>
              <div className="text-emerald-100">Premium Products</div>
            </div>
            <div>
              <div className="text-3xl font-bold mb-2">100%</div>
              <div className="text-emerald-100">Indian Made</div>
            </div>
            <div>
              <div className="text-3xl font-bold mb-2">Eco</div>
              <div className="text-emerald-100">Friendly Formula</div>
            </div>
            <div>
              <div className="text-3xl font-bold mb-2">Safe</div>
              <div className="text-emerald-100">For Family</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Products;