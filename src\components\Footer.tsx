import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Mail, Phone, MapPin, Leaf } from 'lucide-react';

const Footer = () => {
  const [email, setEmail] = useState('');
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');

    try {
      const response = await fetch('/api/newsletter', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const result = await response.json();

      if (response.ok) {
        setIsSubscribed(true);
        setEmail('');
        // Keep success message visible (don't auto-hide as per user preference)
      } else {
        setError(result.error || 'Failed to subscribe. Please try again.');
      }
    } catch (error) {
      console.error('Newsletter subscription error:', error);
      setError('Failed to subscribe. Please check your connection and try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <footer className="bg-emerald-800 text-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg">
          <g fill="none" fillRule="evenodd">
            <g fill="white" fillOpacity="0.3">
              <circle cx="7" cy="7" r="1"/>
              <circle cx="27" cy="7" r="1"/>
              <circle cx="47" cy="7" r="1"/>
              <circle cx="7" cy="27" r="1"/>
              <circle cx="27" cy="27" r="1"/>
              <circle cx="47" cy="27" r="1"/>
              <circle cx="7" cy="47" r="1"/>
              <circle cx="27" cy="47" r="1"/>
              <circle cx="47" cy="47" r="1"/>
            </g>
          </g>
        </svg>
      </div>

      {/* Decorative elements */}
      <div className="absolute inset-0">
        <div className="absolute top-10 right-10 w-32 h-32 bg-emerald-600/20 rounded-full blur-xl"></div>
        <div className="absolute bottom-10 left-10 w-40 h-40 bg-emerald-500/10 rounded-full blur-2xl"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10">
          {/* Brand Section */}
          <div className="space-y-6">
            <div className="flex items-center space-x-4">
              <div className="bg-emerald-600 p-3 rounded-xl shadow-lg">
                <Leaf className="w-7 h-7 text-white" />
              </div>
              <div>
                <h3 className="text-2xl font-bold text-white">Phoenix Klein</h3>
                <p className="text-emerald-300 text-sm font-medium">Laboratories</p>
              </div>
            </div>
            <p className="text-gray-300 text-sm leading-relaxed">
              <span className="text-emerald-400 font-semibold">"WOMEN STRONG, COUNTRY STRONG"</span><br />
              Leading the way in eco-friendly cleaning solutions with Phillipina Green.
              Empowering women while protecting our environment.
            </p>
            <div className="flex space-x-4 pt-2">
              <div className="text-emerald-300 text-sm">
                Follow us on social media for updates and eco-friendly tips!
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-bold text-white mb-6 flex items-center">
              <span className="w-2 h-6 bg-emerald-400 rounded-full mr-3"></span>
              Quick Links
            </h3>
            <ul className="space-y-4">
              {[
                { to: '/products', text: 'Products' },
                { to: '/outlets', text: 'Our Outlets' },
                { to: '/contact', text: 'Contact Us' }
              ].map((item, index) => (
                <li key={index} className="group">
                  <Link
                    to={item.to}
                    className="text-gray-300 hover:text-emerald-400 text-sm transition-all duration-300 flex items-center hover:translate-x-2"
                  >
                    <span className="w-2 h-2 bg-emerald-400 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-all duration-300 transform scale-0 group-hover:scale-100"></span>
                    {item.text}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-bold text-white mb-6 flex items-center">
              <span className="w-2 h-6 bg-emerald-400 rounded-full mr-3"></span>
              Contact Us
            </h3>
            <ul className="space-y-5">
              <li className="flex items-start group">
                <div className="p-2 bg-emerald-700/50 group-hover:bg-emerald-600 rounded-lg mr-4 transition-colors duration-300 backdrop-blur-sm border border-emerald-600/30">
                  <Phone className="w-4 h-4 text-emerald-300 group-hover:text-white" />
                </div>
                <div>
                  <p className="text-emerald-200 text-xs mb-1">Call us</p>
                  <a href="tel:+917537830000" className="text-white hover:text-emerald-300 text-sm transition-colors">
                    +91 75378 30000
                  </a>
                </div>
              </li>
              <li className="flex items-start group">
                <div className="p-2 bg-emerald-700/50 group-hover:bg-emerald-600 rounded-lg mr-4 transition-colors duration-300 backdrop-blur-sm border border-emerald-600/30">
                  <Mail className="w-4 h-4 text-emerald-300 group-hover:text-white" />
                </div>
                <div>
                  <p className="text-emerald-200 text-xs mb-1">Email us</p>
                  <a href="mailto:<EMAIL>" className="text-white hover:text-emerald-300 text-sm transition-colors break-all">
                    <EMAIL>
                  </a>
                </div>
              </li>
              <li className="flex items-start group">
                <div className="p-2 bg-emerald-700/50 group-hover:bg-emerald-600 rounded-lg mr-4 transition-colors duration-300 backdrop-blur-sm border border-emerald-600/30">
                  <MapPin className="w-4 h-4 text-emerald-300 group-hover:text-white" />
                </div>
                <div>
                  <p className="text-emerald-200 text-xs mb-1">Visit us</p>
                  <span className="text-white text-sm leading-relaxed">
                    Plot No. G-820/A, Lodhika GIDC,<br />
                    Road D-2, Metoda (Rajkot)
                  </span>
                </div>
              </li>
            </ul>
          </div>

          {/* Newsletter */}
          <div>
            <h3 className="text-lg font-bold text-white mb-6 flex items-center">
              <span className="w-2 h-6 bg-emerald-400 rounded-full mr-3"></span>
              Stay Updated
            </h3>
            <p className="text-emerald-100 text-sm mb-6 leading-relaxed">
              Subscribe for the latest updates, eco-friendly tips, and exclusive offers from Phillipina Green.
            </p>
            {isSubscribed ? (
              <div className="bg-emerald-600/30 border border-emerald-400 text-emerald-200 p-4 rounded-xl text-sm backdrop-blur-sm">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-emerald-300 rounded-full mr-2"></div>
                    Thank you for subscribing!
                  </div>
                  <button
                    onClick={() => setIsSubscribed(false)}
                    className="text-emerald-300 hover:text-white text-xs underline"
                  >
                    Subscribe another email
                  </button>
                </div>
                <p className="mt-2 text-xs text-emerald-300">
                  You'll receive our latest updates and eco-friendly tips soon!
                </p>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="space-y-4">
                {error && (
                  <div className="bg-red-600/30 border border-red-400 text-red-200 p-3 rounded-xl text-sm backdrop-blur-sm">
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-red-300 rounded-full mr-2"></div>
                      {error}
                    </div>
                  </div>
                )}
                <div>
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email address"
                    className="w-full px-4 py-3 bg-emerald-700/30 border border-emerald-600/50 rounded-xl text-sm text-white placeholder-emerald-200 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all backdrop-blur-sm"
                    required
                    disabled={isSubmitting}
                  />
                </div>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-emerald-600 hover:bg-emerald-500 disabled:bg-emerald-700 disabled:cursor-not-allowed text-white font-semibold py-3 px-4 rounded-xl transition-all duration-300 transform hover:-translate-y-1 hover:shadow-lg shadow-emerald-500/30 disabled:transform-none disabled:shadow-none"
                >
                  {isSubmitting ? 'Subscribing...' : 'Subscribe Now'}
                </button>
              </form>
            )}
          </div>
        </div>

        {/* Copyright */}
        <div className="mt-16 pt-8 border-t border-emerald-700/50">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-emerald-200 text-sm">
              &copy; {new Date().getFullYear()} <span className="text-emerald-300 font-semibold">Phillipina Green</span> by Phoenix Klein Laboratories. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <Link to="/privacy-policy" className="text-emerald-200 hover:text-emerald-300 text-sm transition-colors duration-300">Privacy Policy</Link>
              <Link to="/terms-of-service" className="text-emerald-200 hover:text-emerald-300 text-sm transition-colors duration-300">Terms of Service</Link>
              <Link to="/cookie-policy" className="text-emerald-200 hover:text-emerald-300 text-sm transition-colors duration-300">Cookie Policy</Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;