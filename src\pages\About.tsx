import React from 'react';
import { Award, Users, Leaf, Globe } from 'lucide-react';

const About = () => {
  return (
    <div className="bg-white">
      {/* Hero Section */}
      <div className="relative bg-emerald-800 text-white py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Our Story</h1>
            <p className="text-lg text-emerald-100 max-w-2xl mx-auto">
              Founded with a vision to revolutionize cleaning products in the Philippines,
              we're committed to creating eco-friendly solutions that work.
            </p>
          </div>
        </div>
      </div>

      {/* Mission & Vision */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid md:grid-cols-2 gap-12">
          <div className="space-y-6">
            <h2 className="text-3xl font-bold text-gray-900">Our Mission</h2>
            <p className="text-gray-600">
              To provide Filipino households with effective, environmentally responsible cleaning
              solutions while promoting sustainable practices in our communities.
            </p>
          </div>
          <div className="space-y-6">
            <h2 className="text-3xl font-bold text-gray-900">Our Vision</h2>
            <p className="text-gray-600">
              To be the leading eco-friendly cleaning products brand in Southeast Asia,
              inspiring a cleaner, greener future for generations to come.
            </p>
          </div>
        </div>
      </div>

      {/* Values */}
      <div className="bg-emerald-50 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">Our Values</h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {[
              {
                icon: <Leaf className="w-8 h-8" />,
                title: "Sustainability",
                description: "Environmental responsibility in every product"
              },
              {
                icon: <Award className="w-8 h-8" />,
                title: "Quality",
                description: "Premium products that deliver results"
              },
              {
                icon: <Users className="w-8 h-8" />,
                title: "Community",
                description: "Supporting local communities"
              },
              {
                icon: <Globe className="w-8 h-8" />,
                title: "Innovation",
                description: "Continuous improvement in green technology"
              }
            ].map((value, index) => (
              <div key={index} className="text-center">
                <div className="inline-block p-4 bg-white rounded-full text-emerald-600 shadow-lg mb-4">
                  {value.icon}
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">{value.title}</h3>
                <p className="text-gray-600">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Team Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">Our Leadership Team</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {[
            {
              name: "Maria Reyes",
              position: "CEO & Founder",
              image: "https://images.pexels.com/photos/2381069/pexels-photo-2381069.jpeg"
            },
            {
              name: "Juan Santos",
              position: "Head of Innovation",
              image: "https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg"
            },
            {
              name: "Ana Lim",
              position: "Sustainability Director",
              image: "https://images.pexels.com/photos/3796217/pexels-photo-3796217.jpeg"
            }
          ].map((member, index) => (
            <div key={index} className="text-center">
              <div className="relative w-48 h-48 mx-auto mb-4">
                <img
                  src={member.image}
                  alt={member.name}
                  className="w-full h-full object-cover rounded-full"
                />
              </div>
              <h3 className="text-xl font-semibold text-gray-900">{member.name}</h3>
              <p className="text-emerald-600">{member.position}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default About;