# Simple Image Renaming Script
Write-Host "=== IMAGE RENAMING TOOL ===" -ForegroundColor Cyan

# Define the renaming map
$renameMap = @{
    "Herosection.jpeg" = "hero-main-product.jpeg"
    "WhatsApp Image 2025-06-04 at 5.58.20 PM (1).jpeg" = "product-01-dish-wash.jpeg"
    "WhatsApp Image 2025-06-04 at 5.58.20 PM (2).jpeg" = "product-02-cloth-wash.jpeg"
    "WhatsApp Image 2025-06-04 at 5.58.20 PM.jpeg" = "product-03-toilet-cleaner.jpeg"
    "WhatsApp Image 2025-06-04 at 5.58.21 PM (1).jpeg" = "product-04-glass-cleaner.jpeg"
    "WhatsApp Image 2025-06-04 at 5.58.21 PM.jpeg" = "product-05-bathroom-cleaner.jpeg"
    "WhatsApp Image 2025-06-04 at 5.58.22 PM (1).jpeg" = "product-06-floor-cleaner.jpeg"
    "WhatsApp Image 2025-06-04 at 5.58.22 PM.jpeg" = "product-07-surface-cleaner.jpeg"
    "WhatsApp Image 2025-06-04 at 5.58.23 PM.jpeg" = "product-08-detergent-powder.jpeg"
    "WhatsApp Image 2025-06-04 at 5.58.24 PM.jpeg" = "product-09-neem-soap.jpeg"
    "WhatsApp Image 2025-06-04 at 5.58.28 PM.jpeg" = "owner-smita-godhani.jpeg"
}

$codeFiles = @(
    "src/components/Hero.tsx",
    "src/pages/Home.tsx", 
    "src/pages/Products.tsx"
)

Write-Host "Will rename images and update code references..." -ForegroundColor Yellow
Write-Host ""

# Update code files first
Write-Host "Updating code references..." -ForegroundColor Green
foreach ($file in $codeFiles) {
    if (Test-Path $file) {
        Write-Host "Processing: $file" -ForegroundColor Cyan
        $content = Get-Content $file -Raw
        $updated = $false
        
        foreach ($oldName in $renameMap.Keys) {
            $newName = $renameMap[$oldName]
            $oldPath = "/images/$oldName"
            $newPath = "/images/$newName"
            
            if ($content.Contains($oldPath)) {
                $content = $content.Replace($oldPath, $newPath)
                $updated = $true
                Write-Host "  Updated: $oldName -> $newName" -ForegroundColor Green
            }
        }
        
        if ($updated) {
            Set-Content $file -Value $content -NoNewline
        }
    }
}

# Rename image files
Write-Host ""
Write-Host "Renaming image files..." -ForegroundColor Green
$imageDir = "public/images"
$renamed = 0

foreach ($oldName in $renameMap.Keys) {
    $newName = $renameMap[$oldName]
    $oldPath = Join-Path $imageDir $oldName
    $newPath = Join-Path $imageDir $newName
    
    if (Test-Path $oldPath) {
        if (-not (Test-Path $newPath)) {
            Rename-Item -Path $oldPath -NewName $newName
            Write-Host "  Renamed: $oldName -> $newName" -ForegroundColor Green
            $renamed++
        } else {
            Write-Host "  Skipped: $newName already exists" -ForegroundColor Yellow
        }
    } else {
        Write-Host "  Not found: $oldName" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== COMPLETED ===" -ForegroundColor Magenta
Write-Host "Successfully renamed $renamed image files" -ForegroundColor Green
Write-Host "Updated all code references" -ForegroundColor Green
Write-Host "Your website should work perfectly!" -ForegroundColor Green
