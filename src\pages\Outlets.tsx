import React, { useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { MapPin, Phone, Clock, Navigation, Users, Award, Building2 } from 'lucide-react';
import { useSEO } from '../hooks/useSEO';

const Outlets = () => {
  // SEO optimization for Outlets page
  useSEO({
    title: 'Phillipina Green Outlets | Store Locations - Find Us Near You',
    description: 'Find Phillipina Green outlets and stores near you. Visit our retail locations in Rajkot, Gujarat for premium eco-friendly cleaning products. Expert guidance and product demonstrations available.',
    keywords: 'phillipina green outlets, store locations, rajkot stores, eco-friendly cleaning products store, natural cleaners shop, gujarat outlets',
    canonical: 'https://phillipinagreenliquid.com/outlets',
    ogTitle: 'Phillipina Green Store Locations | Find Outlets Near You',
    ogDescription: 'Visit our retail outlets for hands-on experience with eco-friendly cleaning products. Expert guidance, product demonstrations, and premium natural cleaners.',
    ogImage: 'https://phillipinagreenliquid.com/images/herosection2.png',
    structuredData: {
      "@context": "https://schema.org",
      "@type": "CollectionPage",
      "name": "Phillipina Green Outlets",
      "description": "Store locations and outlets for Phillipina Green eco-friendly cleaning products",
      "url": "https://phillipinagreenliquid.com/outlets",
      "mainEntity": {
        "@type": "ItemList",
        "numberOfItems": 3,
        "itemListElement": [
          {
            "@type": "Store",
            "position": 1,
            "name": "Phillipina Green Liquid Point",
            "address": {
              "@type": "PostalAddress",
              "streetAddress": "Shop No. 21, Vraj Heights, Ambaji Mandir Road, Ambika Township",
              "addressLocality": "Rajkot",
              "addressRegion": "Gujarat",
              "addressCountry": "IN"
            },
            "telephone": "+91 75378 30000",
            "openingHours": "Mo-Sa 09:00-19:00"
          }
        ]
      }
    }
  });

  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const outlets = [
    {
      id: 1,
      name: "Phillipina Green Liquid Point",
      address: "Shop No. 21, Vraj Heights, Ambaji Mandir Road, Ambika Township, Rajkot",
      city: "Rajkot",
      state: "Gujarat",
      phone: "+91 75378 30000",
      email: "<EMAIL>",
      hours: "Mon-Sat: 9:00 AM - 7:00 PM",
      type: "Retail Outlet",
      features: ["Premium Products", "Expert Guidance", "Product Demonstrations", "Customer Support"],
      manager: "Store Manager",
      designation: "Retail Operations"
    },
    {
      id: 2,
      name: "Phillipina Green Liquid Point",
      address: "Satyasai Road, Maruti Chowk, Near Adarsh Bakery, Rajkot",
      city: "Rajkot",
      state: "Gujarat",
      phone: "+91 75378 30000",
      email: "<EMAIL>",
      hours: "Mon-Sat: 9:00 AM - 7:00 PM",
      type: "Retail Outlet",
      features: ["Premium Products", "Expert Guidance", "Product Demonstrations", "Customer Support"],
      manager: "Store Manager",
      designation: "Retail Operations"
    },
    {
      id: 3,
      name: "Phillipina Green Liquid Point",
      address: "Swaminarayan Chowk, Opposite Somanath Hotel, Near Shyam Complex, Kisan Nagar, Mavdi Main Road, Rajkot",
      city: "Rajkot",
      state: "Gujarat",
      phone: "+91 75378 30000",
      email: "<EMAIL>",
      hours: "Mon-Sat: 9:00 AM - 7:00 PM",
      type: "Retail Outlet",
      features: ["Premium Products", "Expert Guidance", "Product Demonstrations", "Customer Support"],
      manager: "Store Manager",
      designation: "Retail Operations"
    }
  ];

  return (
    <div className="bg-emerald-50">
      {/* Hero Section */}
      <div className="relative bg-gradient-to-br from-emerald-600 via-emerald-700 to-emerald-800 overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-72 h-72 bg-emerald-400/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 right-10 w-96 h-96 bg-emerald-300/10 rounded-full blur-3xl"></div>
        </div>

        {/* Floating Particles */}
        <div className="absolute inset-0">
          {[...Array(20)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-white/20 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [-20, -100, -20],
                opacity: [0, 1, 0],
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <motion.div
            className="text-center"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <motion.div
              className="inline-flex items-center px-6 py-3 rounded-full text-sm font-medium bg-emerald-600/20 text-emerald-200 border border-emerald-400/30 backdrop-blur-sm mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.6 }}
            >
              <Building2 size={16} className="mr-2" />
              <span className="mr-2 px-2 py-0.5 bg-emerald-500 text-white rounded-full text-xs font-bold">3</span>
              <span className="text-emerald-200 font-semibold">Convenient Locations in Rajkot</span>
            </motion.div>

            <motion.h1
              className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.8 }}
            >
              Our <span className="text-emerald-300">Outlets</span>
            </motion.h1>

            <motion.p
              className="text-xl md:text-2xl text-emerald-100 max-w-3xl mx-auto leading-relaxed"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.6 }}
            >
              Visit our conveniently located outlets in Rajkot for premium Phillipina Green products,
              expert guidance, and exceptional customer service.
            </motion.p>

            {/* Stats */}
            <motion.div
              className="flex flex-wrap gap-8 justify-center pt-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.6 }}
            >
              {[
                { icon: Building2, label: "Outlets", value: "3" },
                { icon: Users, label: "City Locations", value: "Rajkot" },
                { icon: Award, label: "Years Experience", value: "5+" }
              ].map((stat, index) => (
                <div key={index} className="flex flex-col items-center text-center min-w-[120px]">
                  <div className="flex items-center justify-center w-12 h-12 bg-emerald-600/20 rounded-xl mb-3 backdrop-blur-sm border border-emerald-400/30">
                    <stat.icon className="w-6 h-6 text-emerald-300" />
                  </div>
                  <div className="text-2xl font-bold text-white mb-1">{stat.value}</div>
                  <div className="text-sm text-emerald-200 leading-tight">{stat.label}</div>
                </div>
              ))}
            </motion.div>
          </motion.div>
        </div>
      </div>

      {/* Outlets Section */}
      <section ref={ref} className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Visit Our <span className="text-emerald-600">Locations</span>
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Each Phillipina Green Liquid Point is designed to provide you with premium eco-friendly products,
              expert guidance, and personalized recommendations for your household needs.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {outlets.map((outlet, index) => (
              <motion.div
                key={outlet.id}
                className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden group hover:shadow-2xl transition-all duration-500 hover:-translate-y-2"
                initial={{ opacity: 0, y: 30 }}
                animate={isInView ? { opacity: 1, y: 0 } : {}}
                transition={{ delay: index * 0.2, duration: 0.6 }}
              >
                {/* Header */}
                <div className="bg-gradient-to-r from-emerald-600 to-emerald-700 p-6 text-white">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center mr-3">
                        <Building2 className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold">{outlet.name}</h3>
                        <p className="text-emerald-200 text-sm">{outlet.type}</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Content */}
                <div className="p-6">
                  {/* Address */}
                  <div className="flex items-start mb-4">
                    <MapPin className="w-5 h-5 text-emerald-600 mt-1 flex-shrink-0" />
                    <div className="ml-3">
                      <p className="text-gray-700 leading-relaxed">{outlet.address}</p>
                      <p className="text-emerald-600 font-medium">{outlet.city}, {outlet.state}</p>
                    </div>
                  </div>

                  {/* Contact */}
                  <div className="space-y-3 mb-6">
                    <div className="flex items-center">
                      <Phone className="w-4 h-4 text-emerald-600 mr-3" />
                      <a href={`tel:${outlet.phone}`} className="text-gray-700 hover:text-emerald-600 transition-colors">
                        {outlet.phone}
                      </a>
                    </div>
                    <div className="flex items-center">
                      <Clock className="w-4 h-4 text-emerald-600 mr-3" />
                      <span className="text-gray-700">{outlet.hours}</span>
                    </div>
                  </div>

                  {/* Features */}
                  <div className="mb-6">
                    <h4 className="font-semibold text-gray-900 mb-3">Services Available</h4>
                    <div className="grid grid-cols-2 gap-2">
                      {outlet.features.map((feature, idx) => (
                        <div key={idx} className="flex items-center text-sm text-gray-600">
                          <div className="w-1.5 h-1.5 bg-emerald-500 rounded-full mr-2"></div>
                          {feature}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Manager */}
                  <div className="border-t border-gray-100 pt-4">
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-emerald-100 rounded-full flex items-center justify-center mr-3">
                        <Users className="w-5 h-5 text-emerald-600" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{outlet.manager}</p>
                        <p className="text-sm text-gray-600">{outlet.designation}</p>
                      </div>
                    </div>
                  </div>

                  {/* Action Button */}
                  <div className="mt-6">
                    <a
                      href={`https://maps.google.com/?q=${encodeURIComponent(outlet.address)}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-full bg-emerald-600 hover:bg-emerald-700 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center group"
                    >
                      <Navigation className="w-4 h-4 mr-2 group-hover:translate-x-1 transition-transform" />
                      Get Directions
                    </a>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default Outlets;
