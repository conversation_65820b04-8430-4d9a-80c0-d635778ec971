import React, { useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { Leaf, Users, Home as HomeIcon, Heart, Star, Quote, Sparkles, Award, Shield, ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useSEO } from '../hooks/useSEO';

const Home = () => {
  // SEO optimization for Home page
  useSEO({
    title: 'Phillipina Green - Premium Eco-Friendly Cleaning Products | Natural & Safe Home Cleaners',
    description: 'Discover Phillipina Green\'s premium eco-friendly cleaning products. Natural, safe, and effective solutions for home cleaning. Empowering women while protecting your family and environment. Shop dish wash, cloth wash, toilet cleaners & more.',
    keywords: 'eco-friendly cleaning products, natural cleaning solutions, women empowerment, safe home cleaners, dish wash liquid, cloth wash, toilet cleaner, bathroom cleaner, floor cleaner, detergent powder, neem soap, ayurvedic soap',
    canonical: 'https://phillipinagreenliquid.com/',
    ogTitle: 'Phillipina Green - Premium Eco-Friendly Cleaning Products | Natural & Safe',
    ogDescription: 'Discover premium eco-friendly cleaning products that empower women while protecting your family. Natural, safe, and effective solutions for all your home cleaning needs.',
    ogImage: 'https://phillipinagreenliquid.com/images/herosection2.png',
    structuredData: {
      "@context": "https://schema.org",
      "@type": "WebPage",
      "name": "Phillipina Green - Premium Eco-Friendly Cleaning Products",
      "description": "Homepage of Phillipina Green featuring premium eco-friendly cleaning products",
      "url": "https://phillipinagreenliquid.com/",
      "mainEntity": {
        "@type": "Organization",
        "name": "Phillipina Green",
        "description": "Premium eco-friendly cleaning products that empower women while protecting your family and environment"
      },
      "breadcrumb": {
        "@type": "BreadcrumbList",
        "itemListElement": [
          {
            "@type": "ListItem",
            "position": 1,
            "name": "Home",
            "item": "https://phillipinagreenliquid.com/"
          }
        ]
      }
    }
  });

  return (
    <div className="bg-emerald-50">
      {/* New Hero Section with Scroll Animations */}
      <HeroSection />

      {/* Compact Featured Products Showcase */}
      <FeaturedProductsShowcase />

      {/* Optimized Why Choose Us Section */}
      <CompactWhyChooseUsSection />

      {/* Owner Message Section - Redesigned */}
      <OwnerMessageSection />

      {/* Customer Reviews Section - Streamlined */}
      <CustomerReviewsSection />
    </div>
  );
};

// New Modern Hero Section with Scroll Animations
const HeroSection = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  return (
    <section ref={ref} className="relative min-h-screen flex items-center overflow-hidden bg-emerald-800">
      {/* Animated Background Elements */}
      <div className="absolute inset-0">
        <motion.div
          className="absolute top-20 right-20 w-96 h-96 rounded-full bg-emerald-600/20 blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.5, 0.3]
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute bottom-20 left-20 w-80 h-80 rounded-full bg-emerald-500/20 blur-3xl"
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.2, 0.4, 0.2]
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] rounded-full bg-emerald-400/10 blur-3xl"
          animate={{
            rotate: [0, 360],
            scale: [1, 1.1, 1]
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
        />
      </div>

      {/* Floating Particles */}
      <div className="absolute inset-0">
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-white/20 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [-20, -100, -20],
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          <motion.div 
            className="space-y-8 text-center lg:text-left"
            initial={{ opacity: 0, x: -50 }}
            animate={isInView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.8 }}
          >
            {/* Badge */}
            <motion.div
              className="inline-flex items-center space-x-2"
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : {}}
              transition={{ delay: 0.2, duration: 0.6 }}
            >
              <span className="inline-flex items-center px-6 py-3 rounded-full text-sm font-medium bg-emerald-600/20 text-emerald-200 border border-emerald-400/30 backdrop-blur-sm">
                <Sparkles size={16} className="mr-2" />
                <span className="mr-2 px-2 py-0.5 bg-emerald-500 text-white rounded-full text-xs font-bold">NEW</span>
                <span className="text-emerald-200 font-semibold">🇮🇳 100% Indian-made Excellence</span>
              </span>
            </motion.div>

            {/* Main Heading */}
            <motion.h1
              className="text-5xl md:text-6xl lg:text-7xl font-bold leading-tight"
              initial={{ opacity: 0, y: 30 }}
              animate={isInView ? { opacity: 1, y: 0 } : {}}
              transition={{ delay: 0.4, duration: 0.8 }}
            >
              <span className="block text-white mb-2">
                Naturally Clean.
              </span>
              <span className="block text-emerald-300">
                Brilliantly Green.
              </span>
            </motion.h1>

            {/* Description */}
            <motion.p
              className="text-xl md:text-2xl text-emerald-100 max-w-2xl leading-relaxed"
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : {}}
              transition={{ delay: 0.6, duration: 0.6 }}
            >
              Experience the power of nature with <span className="font-semibold text-emerald-300">Phillipina Green</span>.
              Our premium home cleaning products empower women while protecting your family and environment.
            </motion.p>

            {/* Stats */}
            <motion.div
              className="flex flex-wrap gap-8 justify-center lg:justify-start pt-6"
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : {}}
              transition={{ delay: 0.8, duration: 0.6 }}
            >
              {[
                { icon: Users, label: "Women Empowered", value: "500+" },
                { icon: Shield, label: "Safe & Natural", value: "100%" },
                { icon: Award, label: "Premium Quality", value: "★★★★★" }
              ].map((stat, index) => (
                <div key={index} className="flex flex-col items-center text-center min-w-[120px]">
                  <div className="flex items-center justify-center w-12 h-12 bg-emerald-600/20 rounded-xl mb-3 backdrop-blur-sm border border-emerald-400/30">
                    <stat.icon className="w-6 h-6 text-emerald-300" />
                  </div>
                  <div className="text-2xl font-bold text-white mb-1">{stat.value}</div>
                  <div className="text-sm text-emerald-200 leading-tight">{stat.label}</div>
                </div>
              ))}
            </motion.div>
          </motion.div>

          {/* Product Image Section */}
          <motion.div 
            className="relative"
            initial={{ opacity: 0, x: 50 }}
            animate={isInView ? { opacity: 1, x: 0 } : {}}
            transition={{ delay: 0.4, duration: 0.8 }}
          >
            <div className="relative">
              {/* Glow Effect */}
              <div className="absolute inset-0 bg-emerald-500/30 rounded-3xl blur-2xl transform scale-110"></div>

              {/* Main Product Image */}
              <motion.div
                className="relative z-10 bg-white/10 backdrop-blur-sm rounded-3xl p-8 border border-white/20"
                whileHover={{ scale: 1.02, rotateY: 5 }}
                transition={{ duration: 0.3 }}
              >
                <img
                  src="/images/herosection2.png"
                  alt="Phillipina Green Premium Products"
                  className="w-full h-auto rounded-2xl shadow-2xl"
                />

                {/* Floating Elements */}
                <motion.div
                  className="absolute -top-4 -right-4 bg-emerald-600 rounded-full p-4 shadow-xl border-4 border-white/20"
                  animate={{
                    y: [-5, 5, -5],
                    rotate: [0, 5, 0]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                >
                  <Leaf size={28} className="text-white" />
                </motion.div>

                <motion.div
                  className="absolute -bottom-4 -left-4 bg-emerald-500 rounded-full p-3 shadow-xl border-4 border-white/20"
                  animate={{
                    y: [5, -5, 5],
                    rotate: [0, -5, 0]
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                >
                  <Star size={24} className="text-white" />
                </motion.div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

// Compact Featured Products Showcase (No CTA buttons)
const FeaturedProductsShowcase = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const products = [
    {
      name: "Surface Cleaner",
      image: "/images/surface-cleaner.jpeg",
      features: ["Heavy-duty cleaning", "Metal safe", "Stain removal"]
    },
    {
      name: "Cloth Wash Liquid",
      image: "/images/cloth-wash-liquid.jpeg",
      features: ["Fabric safe", "Long-lasting fragrance", "Color protection"]
    },
    {
      name: "Floor Cleaner",
      image: "/images/floor-cleaner.jpeg",
      features: ["Streak-free shine", "Pleasant aroma", "Quick drying"]
    }
  ];

  return (
    <section ref={ref} className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
        >
          <motion.h2
            className="text-3xl md:text-4xl font-bold text-gray-900 mb-4"
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ delay: 0.2, duration: 0.6 }}
          >
            Our <span className="text-emerald-600">Premium Collection</span>
          </motion.h2>
          <motion.p
            className="text-lg text-gray-600 max-w-2xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ delay: 0.4, duration: 0.6 }}
          >
            Discover our range of eco-friendly cleaning solutions, crafted with care for your family and the environment.
          </motion.p>
        </motion.div>

        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-8"
          initial={{ opacity: 0, y: 40 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ delay: 0.6, duration: 0.8 }}
        >
          {products.map((product, index) => (
            <motion.div
              key={index}
              className="group relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border border-slate-100"
              initial={{ opacity: 0, y: 30 }}
              animate={isInView ? { opacity: 1, y: 0 } : {}}
              transition={{ delay: 0.8 + index * 0.2, duration: 0.6 }}
              whileHover={{ y: -8, scale: 1.02 }}
            >
              <div className="relative overflow-hidden bg-white rounded-t-2xl">
                <img
                  src={product.image}
                  alt={product.name}
                  className="w-full h-64 object-contain group-hover:scale-105 transition-transform duration-500 p-6"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>

              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-emerald-600 transition-colors">
                  {product.name}
                </h3>

                <div className="flex flex-wrap gap-2">
                  {product.features.map((feature, idx) => (
                    <span
                      key={idx}
                      className="inline-flex items-center px-3 py-1 bg-emerald-50 text-emerald-700 text-sm rounded-full border border-emerald-100"
                    >
                      <Leaf className="w-3 h-3 mr-1" />
                      {feature}
                    </span>
                  ))}
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Explore Products Button */}
        <motion.div
          className="flex justify-center mt-12"
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ delay: 1.4, duration: 0.6 }}
        >
          <Link
            to="/products"
            className="group inline-flex items-center px-8 py-4 bg-emerald-600 hover:bg-emerald-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
          >
            <span className="mr-2">Explore All Products</span>
            <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
          </Link>
        </motion.div>
      </div>
    </section>
  );
};

// Enhanced Why Choose Us Section
const CompactWhyChooseUsSection = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const features = [
    {
      title: "Women Empowerment",
      description: "Supporting women with self-respectful livelihood opportunities across India",
      icon: Users,
      gradient: "from-emerald-500 to-emerald-600",
      bgGradient: "from-emerald-50 to-emerald-100",
      iconBg: "bg-gradient-to-br from-emerald-500 to-emerald-600",
      stats: "500+ Women",
      highlight: "Empowered"
    },
    {
      title: "100% Indian-made",
      description: "Proudly manufactured in India with locally sourced premium ingredients",
      icon: HomeIcon,
      gradient: "from-emerald-600 to-emerald-700",
      bgGradient: "from-emerald-50 to-emerald-100",
      iconBg: "bg-gradient-to-br from-emerald-600 to-emerald-700",
      stats: "🇮🇳 Made in",
      highlight: "Gujarat"
    },
    {
      title: "Eco-Friendly Formula",
      description: "Safe, non-acidic formulas that are gentle on hands and environment",
      icon: Heart,
      gradient: "from-emerald-500 to-emerald-700",
      bgGradient: "from-emerald-50 to-emerald-100",
      iconBg: "bg-gradient-to-br from-emerald-500 to-emerald-700",
      stats: "100% Safe",
      highlight: "& Natural"
    }
  ];

  return (
    <section ref={ref} className="py-12 bg-gradient-to-br from-emerald-50 to-white relative overflow-hidden">
      {/* Subtle Background Decoration */}
      <div className="absolute inset-0">
        <div className="absolute top-10 right-10 w-48 h-48 bg-emerald-200/10 rounded-full blur-2xl"></div>
        <div className="absolute bottom-10 left-10 w-48 h-48 bg-emerald-300/10 rounded-full blur-2xl"></div>
      </div>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          className="text-center mb-10"
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6 }}
        >
          <motion.div
            className="inline-flex items-center px-4 py-2 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800 border border-emerald-200 mb-4"
            initial={{ opacity: 0, y: 10 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ delay: 0.1, duration: 0.5 }}
          >
            <Award className="w-3 h-3 mr-2" />
            Why Choose Us
          </motion.div>

          <motion.h2
            className="text-3xl md:text-4xl font-bold text-gray-900 mb-3"
            initial={{ opacity: 0, y: 15 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ delay: 0.2, duration: 0.5 }}
          >
            Why Choose{" "}
            <span className="bg-gradient-to-r from-emerald-600 to-emerald-500 bg-clip-text text-transparent">
              Phillipina Green
            </span>
            ?
          </motion.h2>

          <motion.p
            className="text-gray-600 max-w-2xl mx-auto"
            initial={{ opacity: 0, y: 15 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ delay: 0.3, duration: 0.5 }}
          >
            Discover what makes us the trusted choice for thousands of families across India
          </motion.p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              className="group relative"
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : {}}
              transition={{ delay: index * 0.1 + 0.3, duration: 0.5 }}
            >
              {/* Compact Card */}
              <div className={`relative bg-gradient-to-br ${feature.bgGradient} rounded-2xl p-6 text-center hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border border-emerald-100/50`}>

                {/* Compact Icon */}
                <motion.div
                  className="relative mb-4"
                  whileHover={{ scale: 1.05 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className={`w-14 h-14 ${feature.iconBg} rounded-xl flex items-center justify-center shadow-lg mx-auto relative z-10`}>
                    <feature.icon className="w-7 h-7 text-white" />
                  </div>

                  {/* Stats Badge */}
                  <div className="absolute -top-1 -right-1 bg-white rounded-full px-2 py-0.5 shadow-md border border-gray-100">
                    <span className="text-xs font-bold text-gray-700">{feature.stats}</span>
                  </div>
                </motion.div>

                {/* Compact Content */}
                <h3 className="text-lg font-bold text-gray-900 mb-2">
                  {feature.title}
                </h3>

                <p className="text-gray-600 text-sm leading-relaxed mb-4">
                  {feature.description}
                </p>

                {/* Compact Highlight Badge */}
                <div className={`inline-flex items-center px-3 py-1 bg-gradient-to-r ${feature.gradient} text-white rounded-full text-xs font-semibold shadow-md`}>
                  <Sparkles className="w-3 h-3 mr-1" />
                  {feature.highlight}
                </div>
              </div>

              {/* Connecting Line (for desktop) */}
              {index < features.length - 1 && (
                <div className="hidden md:block absolute top-1/2 -right-3 w-6 h-0.5 bg-gradient-to-r from-emerald-300 to-transparent transform -translate-y-1/2 z-0"></div>
              )}
            </motion.div>
          ))}
        </div>


      </div>
    </section>
  );
};

// Owner Message Section - Redesigned
const OwnerMessageSection = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  return (
    <section ref={ref} className="py-16 bg-white relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-32 h-32 bg-emerald-200/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-emerald-300/20 rounded-full blur-3xl"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          className="flex flex-col lg:flex-row items-center gap-12"
          initial={{ opacity: 0, y: 40 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
        >
          <motion.div
            className="lg:w-1/3 flex justify-center"
            initial={{ opacity: 0, x: -30 }}
            animate={isInView ? { opacity: 1, x: 0 } : {}}
            transition={{ delay: 0.2, duration: 0.6 }}
          >
            <div className="relative w-64 h-64 rounded-2xl overflow-hidden shadow-2xl border-4 border-white transform hover:scale-105 transition-transform duration-300">
              <img
                src="/images/owner-smita-godhani.jpeg"
                alt="Smita Vimal Godhani - Founder & CEO"
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-emerald-900/40"></div>
            </div>
          </motion.div>

          <motion.div
            className="lg:w-2/3 text-center lg:text-left bg-white/80 backdrop-blur-sm p-8 rounded-2xl shadow-xl border border-white/50"
            initial={{ opacity: 0, x: 30 }}
            animate={isInView ? { opacity: 1, x: 0 } : {}}
            transition={{ delay: 0.4, duration: 0.6 }}
          >
            <div className="absolute -top-4 -left-4 bg-emerald-600 p-3 rounded-full shadow-lg">
              <Quote size={28} className="text-white" />
            </div>
            <h2 className="text-3xl font-bold text-gray-900 mb-6">
              A Message from Our Founder
            </h2>
            <blockquote className="text-lg text-gray-700 leading-relaxed mb-6 italic space-y-4">
              <p>"I believe that women are as capable as men. We need to contribute just 2 hours a day to support the economy."</p>
              <p>"That's why I started Phoenix Klein Laboratories to manufacture 100% Indian-made home cleaning products under the brand name <span className="font-semibold text-emerald-600">Phillipina Green</span>."</p>
            </blockquote>
            <div className="flex items-center">
              <div className="w-1 h-16 bg-emerald-600 rounded-full mr-4"></div>
              <div>
                <p className="text-xl font-semibold text-emerald-700">Smita Vimal Godhani</p>
                <p className="text-gray-600">Founder & CEO</p>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

// Customer Reviews Section - Streamlined
const CustomerReviewsSection = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const reviews = [
    {
      name: "Priya Sharma",
      location: "Mumbai",
      rating: 5,
      comment: "Excellent quality! My dishes are spotless and my hands feel soft.",
      product: "Dish Wash"
    },
    {
      name: "Anjali Patel",
      location: "Ahmedabad",
      rating: 5,
      comment: "Love the natural fragrance and how gentle it is on fabrics.",
      product: "Cloth Wash"
    },
    {
      name: "Meera Singh",
      location: "Delhi",
      rating: 5,
      comment: "Best floor cleaner I've used. Leaves floors sparkling clean!",
      product: "Floor Cleaner"
    }
  ];

  return (
    <section ref={ref} className="py-16 bg-emerald-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
        >
          <motion.h2
            className="text-3xl md:text-4xl font-bold text-gray-900 mb-4"
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ delay: 0.2, duration: 0.6 }}
          >
            What Our <span className="text-emerald-600">Customers</span> Say
          </motion.h2>
          <motion.p
            className="text-lg text-gray-600 max-w-2xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ delay: 0.4, duration: 0.6 }}
          >
            Hear from families who have experienced the Phillipina Green difference.
          </motion.p>
        </motion.div>

        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-8"
          initial={{ opacity: 0, y: 40 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ delay: 0.6, duration: 0.8 }}
        >
          {reviews.map((review, index) => (
            <motion.div
              key={index}
              className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-emerald-100"
              initial={{ opacity: 0, y: 30 }}
              animate={isInView ? { opacity: 1, y: 0 } : {}}
              transition={{ delay: 0.8 + index * 0.2, duration: 0.6 }}
              whileHover={{ y: -4 }}
            >
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-emerald-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                  {review.name.charAt(0)}
                </div>
                <div className="ml-4">
                  <h4 className="font-semibold text-gray-900">{review.name}</h4>
                  <p className="text-sm text-gray-600">{review.location}</p>
                </div>
              </div>

              <div className="flex items-center mb-3">
                {[...Array(review.rating)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                ))}
              </div>

              <p className="text-gray-700 mb-4 italic">"{review.comment}"</p>

              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-500">Verified Purchase</span>
                <span className="text-sm font-medium text-emerald-600">Product: {review.product}</span>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default Home;
