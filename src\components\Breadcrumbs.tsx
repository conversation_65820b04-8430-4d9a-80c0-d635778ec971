import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ChevronRight, Home } from 'lucide-react';

interface BreadcrumbItem {
  name: string;
  href: string;
}

const Breadcrumbs: React.FC = () => {
  const location = useLocation();
  const pathnames = location.pathname.split('/').filter((x) => x);

  const breadcrumbNameMap: { [key: string]: string } = {
    'products': 'Products',
    'outlets': 'Outlets', 
    'contact': 'Contact',
    'privacy-policy': 'Privacy Policy',
    'cookie-policy': 'Cookie Policy',
    'terms-of-service': 'Terms of Service'
  };

  const breadcrumbs: BreadcrumbItem[] = [
    { name: 'Home', href: '/' }
  ];

  pathnames.forEach((name, index) => {
    const routeTo = `/${pathnames.slice(0, index + 1).join('/')}`;
    const breadcrumbName = breadcrumbNameMap[name] || name;
    breadcrumbs.push({
      name: breadcrumbName,
      href: routeTo
    });
  });

  // Don't show breadcrumbs on home page
  if (location.pathname === '/') {
    return null;
  }

  // Generate structured data for breadcrumbs
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": breadcrumbs.map((breadcrumb, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": breadcrumb.name,
      "item": `https://phillipinagreenliquid.com${breadcrumb.href === '/' ? '' : breadcrumb.href}`
    }))
  };

  React.useEffect(() => {
    // Add breadcrumb structured data
    const existingScript = document.querySelector('script[data-breadcrumb-structured-data]');
    if (existingScript) {
      existingScript.remove();
    }

    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.setAttribute('data-breadcrumb-structured-data', 'true');
    script.textContent = JSON.stringify(structuredData);
    document.head.appendChild(script);

    return () => {
      const scriptToRemove = document.querySelector('script[data-breadcrumb-structured-data]');
      if (scriptToRemove) {
        scriptToRemove.remove();
      }
    };
  }, [location.pathname]);

  return (
    <nav className="bg-emerald-50 border-b border-emerald-100" aria-label="Breadcrumb">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center space-x-2 py-3 text-sm">
          {breadcrumbs.map((breadcrumb, index) => (
            <div key={breadcrumb.href} className="flex items-center">
              {index > 0 && (
                <ChevronRight className="h-4 w-4 text-emerald-400 mx-2" />
              )}
              {index === 0 && (
                <Home className="h-4 w-4 text-emerald-600 mr-1" />
              )}
              {index === breadcrumbs.length - 1 ? (
                <span className="text-emerald-800 font-medium">
                  {breadcrumb.name}
                </span>
              ) : (
                <Link
                  to={breadcrumb.href}
                  className="text-emerald-600 hover:text-emerald-800 transition-colors duration-200"
                >
                  {breadcrumb.name}
                </Link>
              )}
            </div>
          ))}
        </div>
      </div>
    </nav>
  );
};

export default Breadcrumbs;
